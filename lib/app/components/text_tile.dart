import 'package:flutter/material.dart';
import 'package:guests/constants.dart';

class TextTile extends StatelessWidget {
  final String text;
  final bool isNew;
  final VoidCallback? onPressed;
  final Color color;

  const TextTile(
    this.text, {
    this.onPressed,
    this.isNew = false,
    this.color = Colors.white,
    Key? key,
  }) : super(key: key);

  // factory TextTile.header(String text) {
  //   return TextTile(text);
  // }

  static Widget expand(
    String text, {
    VoidCallback? onPressed,
  }) {
    return ListTile(
      contentPadding: kContentPadding,
      tileColor: Colors.white,
      onTap: onPressed,
      title: Text(
        text ?? '',
        style: const TextStyle(
          fontSize: 16,
          color: Colors.black,
        ),
        textAlign: TextAlign.left,
      ),
      trailing: const Icon(Icons.expand_more),
    );
  }

  static Widget header(String text) {
    return ListTile(
      contentPadding: kContentPadding,
      dense: true,
      title: Text(
        text,
        style: const TextStyle(
          fontSize: 14,
          color: const Color(0xff666666),
        ),
        textAlign: TextAlign.left,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return ListTile(
      onTap: this.onPressed,
      contentPadding: kContentPadding,
      tileColor: Colors.white,
      title: Text(
        this.text,
        style: const TextStyle(
          fontSize: 16,
          color: Colors.black,
        ),
        textAlign: TextAlign.left,
      ),
      trailing: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Visibility(
            visible: this.isNew ?? false,
            child: Container(
              decoration: const BoxDecoration(
                color: kColorRed,
                borderRadius:
                    const BorderRadius.all(const Radius.circular(20.0)),
              ),
              child: const Text(
                'NEW',
                style: const TextStyle(
                  fontSize: 12,
                  color: Colors.white,
                  fontWeight: FontWeight.w700,
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ),
          const Icon(
            Icons.keyboard_arrow_right,
            color: const Color(0xFFB9B9B9),
          ),
        ],
      ),
    );
  }
}
