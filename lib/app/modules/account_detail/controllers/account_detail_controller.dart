import 'dart:async';

import 'package:get/get.dart';
import 'package:guests/app/models/store_account_model.dart';
import 'package:guests/app/providers/account_provider.dart';
import 'package:guests/extension.dart';
import 'package:guests/enums.dart';

class AccountDetailController extends GetxController with StateMixin<String> {
  final AccountProvider accountProvider;
  
  final password = ''.obs;
  final confirmPassword = ''.obs;

  final _id = ''.obs;
  String get id => _id.value;
  bool get isCreating => id.isEmpty;
  bool get isUpdating => id.isNotEmpty;

  final _data = StoreAccount().obs;
  StoreAccount get data => _data.value;

  String get displayButton => isCreating ? '新增' : '修改';
  String get displayTitle => '$displayButton操作員';

  AccountDetailController({
    required this.accountProvider,
  });

  void refreshData() {
    _data.refresh();
  }

  @override
  void onInit() {
    super.onInit();
    if (Get.parameters.containsKey('id')) {
      _id.value = Get.parameters['id'];
    }
  }

  @override
  void onReady() {
    super.onReady();
    onRefresh();
  }

  @override
  void onClose() {
    //
  }

  Future<void> onRefresh() async {
    try {
      change('', status: RxStatus.loading());
      if (id != null && id.isNotEmpty) {
        _data.value = await accountProvider.getStoreAccount(id);
      } else {
        _data.value = StoreAccount(status: Switcher.On.index);
      }
      change('', status: RxStatus.success());
    } catch (e) {
      change('', status: RxStatus.error(e.toString()));
    }
  }

  Future<num> _update() {
    final req = data.toUpdateStoreAccountReq();
    req.password = password.value;
    return accountProvider.updateStoreAccount(id, req);
  }

  Future<num> _create() {
    final req = data.toCreateStoreAccountReq();
    req.password = password.value;
    return accountProvider.createStoreAccount(req);
  }

  Future<num> submit() async {
    final future = isCreating ? _create : _update;
    return await future();
  }
}
