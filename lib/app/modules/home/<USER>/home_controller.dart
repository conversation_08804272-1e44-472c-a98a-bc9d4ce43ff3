import 'dart:async';


import 'package:get/get.dart';
import 'package:guests/app/models/invoice_status_req.dart';
import 'package:guests/app/models/order_detail_model.dart'
    show OrderDetail;
import 'package:guests/app/providers/account_provider.dart';
import 'package:guests/app/providers/api_provider.dart';
import 'package:guests/app/providers/invoice_provider.dart';
import 'package:guests/app/providers/order_provider.dart';
import 'package:guests/app/providers/pref_provider.dart';
import 'package:guests/enums.dart';
import 'package:logger/logger.dart';
import 'package:stream_transform/stream_transform.dart';
import 'package:guests/app/providers/box_provider.dart';
import 'package:guests/app/routes/app_pages.dart';
import 'package:guests/constants.dart';
import 'package:guests/extension.dart';
import 'package:guests/keys.dart';

enum HomeMenu {
  list,
  settings,
}

class HomeController extends GetxController with StateMixin<String> {
  final _homeMenu = HomeMenu.list.obs;
  HomeMenu get homeMenu => _homeMenu.value;
  set homeMenu(HomeMenu value) => _homeMenu.value = value;
  final OrderProvider orderProvider;
  final InvoiceProvider invoiceProvider;
  final AccountProvider accountProvider;
  final _disposable = Completer();
  ApiProvider get apiProvider => orderProvider.apiProvider;
  PrefProvider get prefProvider => apiProvider.prefProvider;
  BoxProvider get boxProvider => prefProvider.boxProvider;
  Logger get logger => boxProvider.logger;

  HomeController({
    required this.orderProvider,
    required this.invoiceProvider,
    required this.accountProvider,
  });

  @override
  void onInit() {
    logger.d('onInit');
    super.onInit();
    _initObservables();
  }

  @override
  void onReady() {
    logger.d('onReady');
    super.onReady();
    _homeMenu.value = HomeMenu.list;
    final token = prefProvider.token;
    boxProvider.token = token;
    boxProvider.userDefault.put(kKeyToken, token);
  }

  void onPostFrame() {
    logger.d('onPostFrame');
  }

  @override
  void onClose() {
    logger.d('onClose');
    _disposable.complete();
    Future(() {
      try {
        boxProvider.close();
      } catch (e) {
        // logger.e(e.message);
      }
    });
    super.onClose();
  }

  void _initObservables() {
    final interval = Stream.periodic(1.minutes).asBroadcastStream();
    // 檢查 token 過期
    interval
        .where((event) {
          final jwt = prefProvider.jwt;
          return jwt.isExpired == true;
        })
        .takeUntil(_disposable.future)
        .listen((event) => prefProvider.token = '');
    // 更新 token
    interval
        .where((event) {
          final jwt = prefProvider.jwt;
          if (jwt.isExpired == true) {
            return false;
          }
          final remainInMinutes = jwt.remainingTime.inMinutes;
          logger.d('[HomeController] token 剩餘: $remainInMinutes 分鐘');
          // 如果 token 還有 3 分鐘就過期，就先更新。
          return remainInMinutes < 3;
        })
        .asyncMap((event) => apiProvider.renew())
        .takeUntil(_disposable.future)
        .listen((token) {
          final jwt = prefProvider.jwt;
          final remainInMinutes = jwt.remainingTime.inMinutes;
          logger.d('[HomeController] token 已更新，剩餘: $remainInMinutes 分鐘');
        });
    // 每分鐘執行檢查同步發票任務
    interval
        .asyncMap((event) => _syncInvoice())
        .takeUntil(_disposable.future)
        .listen((event) {});
    // 每分鐘下載最新訂單
    interval
        .asyncMap((event) => orderProvider.getOrders(page: 1, limit: kLimit))
        .takeUntil(_disposable.future)
        .listen((event) {});
    // token 串流，不同時才會送出事件
    final tokenStream = prefProvider.userDefault
        .watch(key: kKeyToken)
        .distinct((prev, next) => prev.value == next.value)
        .asBroadcastStream();
    // 有效 token，取得設定值
    tokenStream
        .where((event) {
          if ('' == event?.value) {
            logger.d('[HomeController] token is EMPTY');
            return false;
          }
          final jwt = prefProvider.jwt;
          // 暫時性 token 略過
          if (jwt.isTemporary == true) {
            return false;
          }
          // 已過期 token 略過
          if (jwt.isExpired == true) {
            return false;
          }
          return true;
        })
        .asyncMap((event) => onRefresh())
        .takeUntil(this._disposable.future)
        .listen((event) {});
    // 無效 token，清除設定值，回到登入頁
    tokenStream
        .where((event) {
          if ('' != event.value) {
            logger.d('[HomeController] token is NOT empty');
            return false;
          }
          if (Routes.LOGIN == Get.currentRoute) {
            logger.d('[HomeController] Current route is LOGIN');
            return false;
          }
          logger.d('[HomeController] Goto LOGIN');
          logger.d('[HomeController] token(${event.value})');
          return true;
        })
        .asyncMap((event) => Get.offAllNamed(Routes.LOGIN))
        .takeUntil(_disposable.future)
        .listen(
          (event) {
            logger.d('ApiProvider - Goto LOGIN');
          },
        );
    // boxProvider.getLazyBox(Keys.BoxOrderInvoice).then((box) {
    //   box
    //       .watch()
    //       .debounce(1.seconds)
    //       .takeUntil(_disposable.future)
    //       .listen((event) async {
    //     if (event.deleted) {
    //       logger.d('[HomeController] box delete');
    //     } else {
    //       logger.d('[HomeController] box sync');
    //       await _syncInvoice();
    //     }
    //   });
    // });
  }

  Future<void> onRefresh() async {
    try {
      for (var box in boxes) {
        await boxProvider.initGsBox(box);
        await boxProvider.initHiveLazyBox(box);
      }
    } catch (e) {
      logger.e(e);
    }
    // 一定要下載的資料
    // 取得 brand 資訊
    try {
      prefProvider.brandsInfo = await apiProvider.getBrandsInfo();
      logger.d('[HomeController] 取得 brandsInfo');
    } catch (e) {
      logger.e(e);
    }
    // 取得 channel 資訊
    try {
      prefProvider.channelsInfo = await apiProvider.getChannelsInfo();
      logger.d('[HomeController] 取得 channelsInfo');
    } catch (e) {
      logger.e(e);
    }
    // 取得 client 資訊
    try {
      prefProvider.clientInfo = await apiProvider.getClientInfo();
      logger.d('[HomeController] 取得 channelsInfo');
    } catch (e) {
      logger.e(e);
    }
    // 取得 user 資訊
    try {
      final id = prefProvider.jwt.id;
      prefProvider.loginAccount = await accountProvider.getStoreAccount('$id');
      logger.d('[HomeController] 取得 loginAccount');
    } catch (e) {
      logger.e(e);
    }
    // 下載最新訂單
    try {
      await orderProvider.getOrders(page: 1, limit: kLimit);
    } catch (e) {
      logger.e(e);
    }
    // 以上資料必須先取得，才能顯示操作介面
    change('', status: RxStatus.success());
    // 以下資料可非同步取得
    try {
      orderProvider.fetchAllOrders();
    } catch (e) {
      logger.e(e);
    }
  }

  ///
  /// 同步發票狀態
  ///
  Future<void> _syncInvoice() async {
    final box = await boxProvider.getLazyBox(Keys.BoxOrderInvoice);
    for (var key in box.keys) {
      if (key is String) {
        final ls = key.split('.');
        final orderId = ls.first;
        final action = ls.last;
        final orderDetail = await orderProvider.getOrderDetail('$orderId');
        final invoiceNumber = await box.get(key) as String;
        final req = InvoiceStatusReq(
          invoiceDate: orderDetail.createdDateTime!.yMd,
          invoiceNo: invoiceNumber,
        );
        if ('${BpscmInvoiceStatus.Invoice.index}' == action) {
          // 上傳開立發票
          try {
            final invoiceNumber = await _uploadInvoice(orderDetail);
            if (invoiceNumber != null && invoiceNumber.isNotEmpty) {
              await box.delete(key);
            }
          } catch (e) {
            // 檢查
            final status = await invoiceProvider.getInvoiceStatus(req);
            if (status.isInvoice) {
              await box.delete(key);
            }
          }
        } else if ('${BpscmInvoiceStatus.Cancel.index}' == action) {
          // 上傳作廢發票
          try {
            final invoiceNumber = await _uploadCancel(orderDetail);
            if (invoiceNumber != null && invoiceNumber.isNotEmpty) {
              await box.delete(key);
            }
          } catch (e) {
            // 檢查
            final status = await invoiceProvider.getInvoiceStatus(req);
            if (status.isCancel) {
              // 字軌已作廢，回傳字軌
              await box.delete(key);
            }
          }
        }
      }
    }
  }

  ///
  /// 上傳開立發票
  ///
  Future<String> _uploadInvoice(OrderDetail orderDetail) async {
    // 取得訂單詳情
    // final orderDetail = await orderProvider.getOrderDetail(orderId);
    // 字軌
    final invoiceNumber = orderDetail?.invoice?.number ?? '';
    // 檢查字軌狀態
    final req = InvoiceStatusReq(
      invoiceDate: orderDetail.createdDateTime!.yMd,
      invoiceNo: invoiceNumber,
    );
    final status = await invoiceProvider.getInvoiceStatus(req);
    if (status.isUnknown) {
      final invoice = orderDetail.toInvoice();
      // 免稅、應稅、零稅率
      final taxType = prefProvider.taxType;
      // 發票時間
      final invoiceDate = DateTime.fromMillisecondsSinceEpoch(0)
          .add(invoice.invoiceDate.milliseconds);
      final ls = await invoiceProvider.pushInvoice(
        invoiceNo: invoice.invoiceNumber!,
        randomNumber: invoice.randomNumber!,
        seller: invoice.sellerIdentifier!,
        buyer: invoice.buyerIdentifier!,
        orderNo: orderDetail.orderNumber,
        invoiceDate: invoiceDate.yMdHms,
        price: orderDetail.total!,
        itemName: orderDetail.productName,
        taxType: taxType,
      );
      if (ls.isNotEmpty) {
        final element = ls.first;
        if ('OK' == element.status) {
          return element.invoiceNo!;
        }
        throw element.message ?? 'Unknown error';
      }
      throw '上傳發票失敗: 字軌($invoiceNumber)';
    }
    return invoiceNumber;
  }

  ///
  /// 上傳作廢發票
  ///
  Future<String> _uploadCancel(OrderDetail orderDetail) async {
    // 取得訂單詳情
    // final orderDetail = await orderProvider.getOrderDetail(orderId);
    // 字軌
    final invoiceNumber = orderDetail?.invoice?.number ?? '';
    // 檢查字軌狀態
    final req = InvoiceStatusReq(
      invoiceDate: orderDetail.createdDateTime!.yMd,
      invoiceNo: invoiceNumber,
    );
    final status = await invoiceProvider.getInvoiceStatus(req);
    if (status.isInvoice) {
      // 發票物件
      final invoice = orderDetail.toInvoice();
      // 發票時間
      final invoiceDate = DateTime.fromMillisecondsSinceEpoch(0)
          .add(invoice.invoiceDate.milliseconds);
      final ls = await invoiceProvider.editInvoice(
        seller: invoice.sellerIdentifier!,
        invoiceNo: invoice.invoiceNumber!,
        // orderNo: this.cached.value.orderNumber,
        invoiceDate: invoiceDate.yMdHms,
        cancelDate: orderDetail.updatedDateTime!.yMdHms,
        buyer: invoice.buyerIdentifier!,
      );
      // 檢查
      if (ls.isNotEmpty) {
        final element = ls.first;
        if ('OK' == element.status) {
          return element.invoiceNo!;
        }
        throw element.message ?? 'Unknown error';
      }
      throw '上傳作廢失敗: 字軌($invoiceNumber)';
    }
    return invoiceNumber;
  }
}
