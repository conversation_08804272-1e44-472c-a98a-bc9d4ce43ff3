import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
import 'package:guests/app/providers/api_provider.dart';
import 'package:guests/app/providers/pref_provider.dart';

class InvoiceSettingsController extends GetxController {
  final gKey = GlobalKey<FormState>();
  final invoiceEnabled = false.obs;
  final invoiceSkipped = false.obs;
  final invoiceTaxId = ''.obs;
  final ApiProvider apiProvider;
  final itemName = ''.obs;
  final taxType = 1.obs;

  PrefProvider get prefProvider => this.apiProvider.prefProvider;

  InvoiceSettingsController({
    required this.apiProvider,
  });

  @override
  void onInit() {
    super.onInit();
    this.invoiceTaxId.value = this.prefProvider.taxId;
    this.itemName.value = this.prefProvider.itemName;
    this.taxType.value = this.prefProvider.taxType.toInt();
  }

  @override
  void onReady() {
    //
  }

  @override
  void onClose() {
    //
  }

  void onPostFrame() {
    this.invoiceEnabled.value = this.prefProvider.invoiceEnabled;
    this.invoiceSkipped.value = this.prefProvider.invoiceSkipped;
  }

  Future<void> onSubmitClicked() async {
    this.prefProvider.invoiceEnabled = invoiceEnabled.value;
    this.prefProvider.invoiceSkipped = invoiceSkipped.value;
    this.prefProvider.itemName = this.itemName.value;
    this.prefProvider.taxType = taxType.value;
  }
}
