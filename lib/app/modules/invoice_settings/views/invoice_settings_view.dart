import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:guests/app/components/custom_scaffold.dart';
import 'package:guests/app/components/dialog_actions.dart';
import 'package:guests/app/modules/invoice_settings/controllers/invoice_settings_controller.dart';
import 'package:guests/app/components/BottomButton.dart';
import 'package:guests/app/components/custom_editor.dart';
import 'package:guests/app/components/text_tile.dart';
import 'package:guests/constants.dart';
import 'package:guests/extension.dart';

class InvoiceSettingsView extends GetView<InvoiceSettingsController> {
  InvoiceSettingsView() {
    this.initializeController().then((_) {
      Get.find<InvoiceSettingsController>()?.onPostFrame();
    });
  }
  @override
  Widget build(BuildContext context) {
    return CustomScaffold(
      clipped: false,
      titleText: '發票設定',
      child: Stack(
        children: [
          Column(
            mainAxisSize: MainAxisSize.max,
            children: [
              Padding(
                padding: EdgeInsets.symmetric(vertical: kPadding),
                child: Text(
                  '請填寫發票資訊',
                  style: TextStyle(
                    fontSize: 16,
                    color: kColorPrimary,
                    fontWeight: FontWeight.w700,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
              Expanded(
                child: SingleChildScrollView(
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Container(
                        color: Colors.white,
                        child: Obx(() {
                          return SwitchListTile.adaptive(
                            contentPadding: kContentPadding,
                            title: Text(
                              '啟用電子發票功能',
                              style: TextStyle(
                                fontSize: 16,
                                color: Colors.black,
                              ),
                              textAlign: TextAlign.left,
                            ),
                            value: this.controller.invoiceEnabled.value,
                            onChanged: this.controller.invoiceEnabled,
                          );
                        }),
                      ),
                      Obx(() {
                        return Visibility(
                          visible: this.controller.invoiceEnabled.value,
                          child: _Page(),
                        );
                      }),
                    ],
                  ),
                ),
              ),
            ],
          ),
          Align(
            alignment: Alignment.bottomCenter,
            child: BottomButton(
              '儲存',
              onPressed: () {
                this.controller.onSubmitClicked().then(
                  (value) {
                    Get.back();
                  },
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}

class _Page extends GetView<InvoiceSettingsController> {
  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        _Page1(),
        _Page2(),
        _Page3(),
        SizedBox(
          height: kBottomPadding,
        ),
      ],
    );
  }
}

class _Page1 extends GetView<InvoiceSettingsController> {
  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        TextTile.header('商家資訊'),
        ListTile(
          tileColor: Colors.white,
          contentPadding: kContentPadding,
          leading: Text(
            '統一編號',
            style: const TextStyle(
              fontSize: 16,
              color: Colors.black,
            ),
          ),
          title: Obx(() {
            return Text(
              this.controller.invoiceTaxId.value ?? '',
              style: const TextStyle(
                fontSize: 16,
                color: Colors.black,
              ),
            );
          }),
        ),
        const SizedBox(
          height: 1.0,
        ),
        ListTile(
          tileColor: Colors.white,
          contentPadding: kContentPadding,
          title: TextFormField(
            initialValue: controller.itemName.value ?? '',
            onChanged: controller.itemName,
            decoration: InputDecoration(
              contentPadding: EdgeInsets.zero,
              labelText: '預設商品名稱',
              hintText: '請輸入預設商品名稱',
            ),
          ),
        ),
        // Form(
        //   key: this.controller.gKey,
        //   child: ListTile(
        //     tileColor: Colors.white,
        //     contentPadding: kContentPadding,
        //     title: TextFormField(
        //       validator: (value) {
        //         // value = this.controller.invoiceTaxId.value;
        //         if (value.isEmpty) {
        //           return null;
        //         }
        //         if (GetUtils.hasMatch(value, r'\d{8}')) {
        //           return null;
        //         }
        //         return '輸入8碼統一編號';
        //       },
        //       initialValue: this.controller.invoiceTaxId.value,
        //       onChanged: this.controller.invoiceTaxId,
        //       textInputAction: TextInputAction.done,
        //       keyboardType: TextInputType.number,
        //       textAlign: TextAlign.start,
        //       decoration: InputDecoration(
        //         contentPadding: EdgeInsets.zero,
        //         labelText: '統一編號',
        //         hintText: '請輸入商家統一編號',
        //       ),
        //       inputFormatters: [
        //         FilteringTextInputFormatter.singleLineFormatter,
        //         FilteringTextInputFormatter.digitsOnly,
        //         LengthLimitingTextInputFormatter(8),
        //       ],
        //     ),
        //   ),
        // ),
      ],
    );
  }
}

class _Page2 extends GetView<InvoiceSettingsController> {
  @override
  Widget build(BuildContext context) {
    // return Obx(() {
    return Column(
      children: [
        TextTile.header('加值中心'),
        TextTile.expand('金財通', onPressed: () {
          DialogActions.show(
            titleText: '請選擇加值中心',
            actions: [
              '金財通',
            ],
          );
        }),
        Visibility(
          visible: false,
          child: CustomEditor(
            labelText: 'API Key',
            hintText: '請輸入API Key',
          ),
        ),
      ],
    );
    // });
  }
}

class _Page3 extends GetView<InvoiceSettingsController> {
  final _actions = const <Map<String, dynamic>>[
    {
      kKeyKey: '應稅：5%稅率',
      kKeyVal: 1,
    },
    {
      kKeyKey: '免稅(農產品)',
      kKeyVal: 3,
    },
  ];

  Future<void> _showDialog() async {
    final index = await DialogActions.show<num>(
      titleText: '選擇稅率',
      actions: ['應稅：5%稅率', '免稅(農產品)'],
    );
    if (index == 0) {
      controller.taxType.value = 1;
      controller.taxType.refresh();
    } else if (index == 1) {
      controller.taxType.value = 3;
      controller.taxType.refresh();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        TextTile.header('稅率'),
        Obx(() {
          final data = this._actions.firstWhere((e) {
            return e[kKeyVal] == controller.taxType.value;
          });
          return TextTile.expand(
            data[kKeyKey] ?? '',
            onPressed: this._showDialog,
          );
        }),
        const SizedBox(
          height: 1.0,
        ),
        ColoredBox(
          color: Colors.white,
          child: Obx(() {
            return SwitchListTile.adaptive(
              contentPadding: kContentPadding,
              title: Text(
                '單筆訂單免開發票',
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.black,
                ),
                textAlign: TextAlign.left,
              ),
              value: this.controller.invoiceSkipped.value,
              onChanged: this.controller.invoiceSkipped,
            );
          }),
        ),
      ],
    );
  }
}
