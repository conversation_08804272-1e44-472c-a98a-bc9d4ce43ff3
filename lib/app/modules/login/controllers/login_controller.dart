import 'dart:async';

import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
import 'package:stream_transform/stream_transform.dart';
import 'package:guests/app/models/login_req.dart';
import 'package:guests/app/models/login_res.dart';
import 'package:guests/app/providers/api_provider.dart';
import 'package:guests/app/providers/box_provider.dart';
import 'package:guests/app/providers/pref_provider.dart';
import 'package:guests/constants.dart';
import 'package:guests/extension.dart';
import 'package:hive/hive.dart';
import 'package:package_info/package_info.dart';

class LoginController extends GetxController
    with StateMixin<String>, WidgetsBindingObserver {
  final _disposable = Completer();
  final bottomInset = (0.0).obs;
  final _rememberMe = false.obs;
  final ApiProvider apiProvider;

  PrefProvider get prefProvider => apiProvider.prefProvider;
  BoxProvider get boxProvider => prefProvider.boxProvider;
  PackageInfo get packageInfo => prefProvider.packageInfo;
  String get appName => packageInfo?.appName ?? '';
  String get version => packageInfo?.displayVersion ?? '';
  Box get userDefault => boxProvider.userDefault;
  bool get rememberMe => _rememberMe.value;
  set rememberMe(bool value) => _rememberMe.value = value;

  final _draft = LoginReq().obs;
  LoginReq get draft => _draft.value;

  LoginController({
    required this.apiProvider,
  });

  @override
  void didChangeMetrics() {
    Get.engine.addPostFrameCallback((duration) {
      this.bottomInset.value = Get.mediaQuery.viewInsets.bottom;
    });
  }

  @override
  void onInit() {
    super.onInit();
    // ref: https://stackoverflow.com/a/63241409
    Get.engine.addObserver(this);
    _rememberMe.stream
        .debounce(500.milliseconds)
        .asyncMapSample((event) {
          kLogger.d('[LoginController] save rememberMe($event)');
          return userDefault.put(kKeyRememberMe, event);
        })
        .takeUntil(_disposable.future)
        .listen((event) {});
  }

  @override
  void onReady() {
    super.onReady();
    onRefresh();
  }

  @override
  void onClose() {
    _disposable.complete();
    Get.engine.removeObserver(this);
  }

  Future<void> onRefresh() async {
    try {
      _rememberMe.value = userDefault.get(kKeyRememberMe, defaultValue: false);
    } catch (e) {
      kLogger.e('[LoginViewController] onRefresh: $e');
      _rememberMe.value = false;
    }
    if (rememberMe) {
      draft.clientCode = userDefault.get(kKeyClientCode, defaultValue: '');
      draft.channelCode = userDefault.get(kKeyChannelCode, defaultValue: '');
      draft.username = userDefault.get(kKeyUsername, defaultValue: '');
    }
    change('', status: RxStatus.success());
  }

  Future<LoginRes> login() async {
    return await apiProvider.login(draft);
  }

  Future<void> onCheckBoxClicked() async {
    final checked = rememberMe;
    kLogger.d('[LoginViewController] onCheckBoxClicked: checked($checked)');
    rememberMe = !checked;
    if (rememberMe == false) {
      await _resetRememberMe();
    }
  }

  Future<void> _resetRememberMe() async {
    kLogger.d('[LoginViewController] _resetRememberMe');
    await userDefault.put(kKeyClientCode, '');
    await userDefault.put(kKeyChannelCode, '');
    await userDefault.put(kKeyUsername, '');
  }

  Future<void> saveRememberMe() async {
    kLogger.d('[LoginViewController] _saveRememberMe');
    await userDefault.put(kKeyClientCode, draft.clientCode);
    await userDefault.put(kKeyChannelCode, draft.channelCode);
    await userDefault.put(kKeyUsername, draft.username);
  }
}
