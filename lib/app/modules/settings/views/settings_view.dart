import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:guests/app/components/background.dart';
import 'package:guests/app/components/dialog_custom.dart';
import 'package:guests/app/components/primary_background.dart';
import 'package:guests/app/modules/settings/controllers/settings_controller.dart';
import 'package:guests/app/routes/app_pages.dart';
import 'package:guests/app/components/Avatar.dart';
import 'package:guests/constants.dart';
import 'package:guests/enums.dart';
import 'package:guests/extension.dart';

class SettingsView extends GetView<SettingsController> {
  SettingsView({
    Key? key,
  }) : super(key: key) {
    initializeController().then(
      (value) {
        final controller = Get.find<SettingsController>();
        controller?.onPostFrame();
      },
    );
  }

  void _onLogoutClicked() async {
    final button = await DialogCustom.showConfirm(
      titleText: '確認',
      contentText: '即將登出',
      rightButtonText: '登出',
    );
    if (button != null && button.isPositive) {
      controller.apiProvider.logout();
    }
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<SettingsController>(
      init: SettingsController(
        accountProvider: Get.find(),
        packageInfo: Get.find(),
      ),
      builder: (controller) {
        return PrimaryBackground(
          child: Material(
            color: Colors.transparent,
            child: SafeArea(
              child: _main(),
            ),
          ),
        );
      },
    );
  }

  Widget _main() {
    return CustomScrollView(
      slivers: [
        _header().sliverBox,
        // _Header(
        //   brandName: controller.prefProvider.brandsInfo?.name,
        //   channelName: controller.prefProvider.channelsInfo?.name,
        //   onPressed: _onLogoutClicked,
        // ).sliverBox,
        SliverFillRemaining(
          hasScrollBody: false,
          fillOverscroll: true,
          child: DecoratedBox(
            decoration: const BoxDecoration(
              borderRadius: kTopRadius,
              color: kColorBackground,
            ),
            child: controller.obx(
              (state) => Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: _children().toList(growable: false),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Iterable<Widget> _children() sync* {
    yield Padding(
      padding: const EdgeInsets.symmetric(
        vertical: 16.0,
        horizontal: kPadding,
      ),
      child: Avatar(
        data: controller.prefProvider.loginAccount,
        showStatus: false,
      ),
    );
    yield ColoredBox(
      color: Colors.white,
      child: _Page(),
    );
    yield SizedBox(height: 36.0);
    yield Center(
      child: Text(
        // 'APP版本：V1.0.0',
        controller?.packageInfo?.displayVersion ?? '',
        style: const TextStyle(
          fontSize: 16,
          color: const Color(0xffb9b9b9),
        ),
        textAlign: TextAlign.center,
      ),
    );
    yield SizedBox(height: kBottomPadding);
  }

  Widget _header() {
    Iterable<Widget> children() sync* {
      yield SizedBox(
        width: double.infinity,
        height: 12.dh,
      );
      yield SvgPicture.asset(
        'assets/images/icon_store.svg',
        width: 90.dw,
        height: 90.dh,
      );
      yield SizedBox(
        height: 12.dh,
      );
      yield Text(
        // '賣東西零售店',
        controller.prefProvider.brandsInfo?.name ?? '',
        style: TextStyle(
          fontSize: 16,
          color: Colors.white,
          fontWeight: FontWeight.w700,
          height: 1.1,
        ),
        textAlign: TextAlign.center,
      );
      yield SizedBox(
        height: 8.dh,
      );
      yield Text(
        // '中山分店',
        controller.prefProvider.channelsInfo?.name ?? '',
        style: TextStyle(
          fontSize: 14,
          color: Colors.white,
          height: 1.0,
        ),
        textAlign: TextAlign.center,
      );
      yield SizedBox(
        height: 10.dh,
      );
    }

    return Background(
      background: Align(
        alignment: Alignment.topRight,
        child: TextButton(
          onPressed: _onLogoutClicked,
          child: Text(
            '登出',
            style: TextStyle(
              fontSize: 16,
              color: Colors.white,
            ),
            textAlign: TextAlign.center,
          ),
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: children().toList(growable: false),
      ),
    );
  }
}


class _Page extends GetView<SettingsController> {
  const _Page({
    Key? key,
  }) : super(key: key);

  Iterable<Widget> _menuItems() sync* {
    final role = controller.prefProvider.jwt.roleId.storeRole;
    // 修改密碼
    if ([StoreRole.Boss, StoreRole.Employee].contains(role)) {
      yield _MenuItem(
        onPressed: () => Get.toNamed(Routes.RESET_PASSWORD),
        titleText: '修改密碼',
      );
    }
    // 操作員帳號設定
    if ([StoreRole.Boss].contains(role)) {
      yield _MenuItem(
        onPressed: () => Get.toNamed(Routes.ACCOUNT_LIST),
        titleText: '操作員帳號設定',
      );
    }
    // 電子發票設定
    if ([StoreRole.Boss, StoreRole.Employee].contains(role)) {
      yield _MenuItem(
        onPressed: () => Get.toNamed(Routes.INVOICE_SETTINGS),
        titleText: '電子發票設定',
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: _menuItems().toList(growable: false),
      // children: List.generate(
      //   controller.actions.length,
      //   (index) {
      //     final jwt = controller.prefProvider.jwt;
      //     final data = controller.actions.elementAt(index);
      //     final visible = (data['authorized'] as Iterable).contains(jwt.roleId);
      //     return Visibility(
      //       visible: visible,
      //       child: _MenuItem(
      //         onPressed: () {
      //           controller.onMenuPressed(index);
      //         },
      //         titleText: data[kKeyName],
      //       ).paddingSymmetric(
      //         horizontal: kPadding,
      //       ),
      //     );
      //   },
      // ),
    );
  }
}

class _MenuItem extends StatelessWidget {
  final VoidCallback? onPressed;
  final String? titleText;

  const _MenuItem({
    Key? key,
    this.onPressed,
    this.titleText,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: kPadding),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          ListTile(
            onTap: this.onPressed,
            dense: true,
            contentPadding: EdgeInsets.zero,
            title: Text(
              this.titleText ?? '',
              style: const TextStyle(
                fontSize: 16,
                color: Colors.black,
                height: 2.5,
              ),
              textHeightBehavior:
                  const TextHeightBehavior(applyHeightToFirstAscent: false),
              textAlign: TextAlign.left,
            ),
            trailing: const Icon(Icons.keyboard_arrow_right),
          ),
          const Divider(
            height: 1.0,
          ),
        ],
      ),
    );
  }
}
