import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'dart:math';

import 'package:dio/dio.dart';
import 'package:encrypt/encrypt.dart';
import 'package:flutter/material.dart' hide Key;
import 'package:flutter_sunmi_printer/flutter_sunmi_printer.dart' as Sunmi;
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:sizer/sizer.dart';
import 'package:hive/hive.dart';
import 'package:package_info/package_info.dart';

import 'app/providers/pref_provider.dart';
import 'app/components/dialog_custom.dart';
import 'app/components/progressing.dart';
import 'app/models/create_store_account_req.dart';
import 'app/models/channels_info_model.dart';
import 'app/models/channels_info_other.dart';
import 'app/models/invoice_model.dart';
import 'app/models/jwt_model.dart' show Jwt;
import 'app/models/order_creating_model.dart';
import 'app/models/order_detail_model.dart'
    show OrderDetail, OrderPayment, Invoice;
import 'app/models/order_model.dart' show Order;

import 'app/models/store_account_model.dart';
import 'app/models/update_store_account_req.dart';
import 'constants.dart';
import 'enums.dart';

DateTime? utcToLocal(String? value) {
  if (value == null) return null;
  var ret = DateTime.tryParse('${value}z');
  if (ret == null) {
    ret = DateTime.tryParse(value);
  }
  return ret?.toLocal();
}

extension ExtensionDioError on DioException {
  bool get isSocketException => error is SocketException;
}

extension ExtensionJwt on Jwt {
  bool get isTemporary => 'temporary' == tokenType;

  DateTime get expirationDateTime {
    final seconds = this.exp ?? 0;
    final duration = Duration(seconds: seconds.toInt());
    return DateTime.fromMillisecondsSinceEpoch(0).add(duration);
  }

  bool get isExpired {
    final now = DateTime.now();
    return now.isAfter(expirationDateTime);
  }

  Duration get remainingTime {
    final now = DateTime.now();
    return expirationDateTime.difference(now);
  }

  num get roleId => role?.id ?? 0;

  String get dir => '$channelId.$clientId.$brandId';
}

extension ExtensionStatelessWidget on Widget {
  Future<bool> initializeController() {
    Completer<bool> completer = new Completer<bool>();

    /// Callback called after widget has been fully built
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      completer.complete(true);
    });

    return completer.future;
  }

  // 可以把任何 Widget 使用 Dialog 形式呈現
  Future<T?> dialog<T>({
    bool barrierDismissible = true,
    EdgeInsets? insetPadding,
  }) {
    return Get.dialog<T>(
      Dialog(
        insetPadding: insetPadding ??
            const EdgeInsets.symmetric(
              // horizontal: 40.0,
              vertical: 24.0,
            ),
        backgroundColor: Colors.transparent,
        elevation: 0.0,
        child: SizedBox(
          width: 300.dw,
          child: this,
        ),
      ),
      barrierDismissible: barrierDismissible,
    );
  }

  // 可以把任何 Widget 使用 Sheet 形式呈現
  Future<T?> sheet<T>({
    final bool? header,
    final bool? isDismissible,
    final bool? enableDrag,
    final bool? isScrollControlled,
    final bool? ignoreSafeArea,
  }) {
    return Get.bottomSheet<T>(
      this,
      isScrollControlled: isScrollControlled ?? true,
      enableDrag: enableDrag ?? false,
      ignoreSafeArea: ignoreSafeArea ?? false,
      isDismissible: isDismissible ?? true,
    );
  }
}

extension ExtensionGetxController on GetxController {
  Future<bool> initializeController() {
    final completer = new Completer<bool>();

    /// Callback called after widget has been fully built
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      completer.complete(true);
    });

    return completer.future;
  }
}

extension ExtensionStoreAccount on StoreAccount {
  bool match(String? value) {
    if (value == null || value.isEmpty) {
      return true;
    }
    final regExp = RegExp(
      value,
      caseSensitive: false,
    );
    if (regExp.hasMatch(name ?? '')) {
      return true;
    }
    if (regExp.hasMatch(comment ?? '')) {
      return true;
    }
    if (regExp.hasMatch(username ?? '')) {
      return true;
    }
    if (regExp.hasMatch(role?.name ?? '')) {
      return true;
    }
    return false;
  }

  // TODO: refactor
  StoreRole get storeRole {
    if (StoreRole.Boss.index == roleId) {
      return StoreRole.Boss;
    }
    if (StoreRole.Employee.index == roleId) {
      return StoreRole.Employee;
    }
    return StoreRole.Employee;
  }

  // TODO: refactor
  set roleId(num? value) {
    role ??= Role();
    role!.id = value ?? StoreRole.Employee.index;
    role!.name = storeRole.name;
  }

  // TODO: refactor
  num get roleId {
    role ??= Role();
    role!.id ??= StoreRole.Employee.index;
    return role!.id!;
  }

  String get displayRoleId {
    if (StoreRole.Boss.index == roleId) {
      return StoreRole.Boss.name;
    }
    if (StoreRole.Employee.index == roleId) {
      return StoreRole.Employee.name;
    }
    return '請選擇身份'; // TODO: i18n
  }

  String get displayLastLogin {
    final dt = utcToLocal(lastLogin);
    return dt != null ? kDateTimeFormat.format(dt) : '';
  }

  DateTime get dateTime {
    return updatedTimeAt ??
        createdTimeAt ??
        DateTime.fromMillisecondsSinceEpoch(0);
  }

  DateTime? get createdTimeAt => utcToLocal(createdAt);

  DateTime? get updatedTimeAt => utcToLocal(updatedAt);

  String get displayStatus => enabled ? '啟用' : '停用';

  Color get displayStatusColor => enabled ? kColorPrimary : Color(0xffb9b9b9);

  bool get enabled => Switcher.On.index == status;
  set enabled(bool value) {
    status = value ? Switcher.On.index : Switcher.Off.index;
  }

  UpdateStoreAccountReq toUpdateStoreAccountReq() {
    return UpdateStoreAccountReq(
      roleId: roleId,
      name: name,
      status: status,
      comment: comment,
    );
  }

  CreateStoreAccountReq toCreateStoreAccountReq() {
    return CreateStoreAccountReq(
      roleId: roleId,
      username: username,
      name: name,
      status: status,
      comment: comment,
    );
  }
}

extension NumX on num {
  String get currency => kNumFormat.format(this);

  StoreRole get storeRole {
    if (this >= 0 && this < StoreRole.values.length) {
      return StoreRole.values.elementAt(this.toInt());
    }
    return StoreRole.Max;
  }

  Switcher get switcher {
    if (this >= 0 && this < Switcher.values.length) {
      return Switcher.values.elementAt(this.toInt());
    }
    return Switcher.Max;
  }

  double get dh => (toDouble() * Constants.designHeightRatio).h;
  double get dw => (toDouble() * Constants.designWidthRatio).w;
  double get dsp => toDouble().sp;
}

extension ExtensionOrderCreating on OrderCreating {
  // 總價
  num get calculateTotal {
    final x = this.subtotal ??= 0;
    final y = this.discount ??= 0;
    final z = this.additionalCharges ??= 0;
    this.productPrice = x;
    this.total = max(0, x - y + z);
    return this.total!;
  }

  // 找零
  String get displayChange {
    final x = this.paid ??= 0;
    final y = this.calculateTotal;
    this.change = x - y;
    final ret = max<num>(0, this.change!);
    return ret.currency;
  }

  DateTime? get createdDateTime {
    return utcToLocal(this.createdAt);
  }

  bool get containsInvoice {
    return invoiceNumber != null && invoiceNumber!.isNotEmpty;
  }
}


extension ExtensionDateTimeRange on DateTimeRange {
  bool contains(DateTime value) {
    final ms = value.millisecondsSinceEpoch;
    return (start.millisecondsSinceEpoch <= ms) &&
        (ms < end.millisecondsSinceEpoch);
  }

  bool outOfRange(DateTime value) {
    return !this.contains(value);
  }
}

extension ExtensionOrder on Order {
  num get orderSerialNumber {
    return num.tryParse(this.orderSerial) ?? 0;
  }

  String get orderSerial {
    return this.orderNumber?.takeLast(6) ?? '000000';
  }

  bool isStatus(num value) {
    return value == this.status;
  }

  bool match(String? value) {
    if (value == null || value.isEmpty) {
      return true;
    }
    final regexp = RegExp(value, caseSensitive: false);
    if (regexp.hasMatch(memberName ?? '')) {
      return true;
    }
    if (regexp.hasMatch(orderNumber ?? '')) {
      return true;
    }
    return false;
  }

  bool contains(DateTimeRange value) {
    final time = this.createdTime;
    return time != null ? value.contains(time) : false;
  }

  DateTime? get createdTime {
    return utcToLocal(this.createdAt);
  }

  String get displayDateTime {
    final time = this.createdTime;
    return time != null ? kDateTimeFormat.format(time) : '';
  }

  String get displayDateTimeMdHm {
    final time = this.createdTime;
    return time != null ? kDateTimeFormatMdHm.format(time) : '';
  }

  bool get isRefund => 5 == this.status;
  bool get isCompleted => 2 == this.status;

  Color get displayStatusColor {
    switch (this.status) {
      case 0:
        return Colors.black;
      case 1:
        return Colors.black;
      case 2:
        return Colors.black;
      case 3:
        return Colors.red;
      case 4:
        return Colors.red;
      case 5:
        return Colors.red;
      default:
        return Colors.black;
    }
  }

  String get displayStatus {
    switch (this.status) {
      case 0:
        return 'order_status_0'.tr;
      case 1:
        return 'order_status_1'.tr;
      case 2:
        return 'order_status_2'.tr;
      case 3:
        return 'order_status_3'.tr;
      case 4:
        return 'order_status_4'.tr;
      case 5:
        // return 'order_status_5'.tr;
        return '訂單取消';
      default:
        return 'status_unknown'.tr;
    }
  }

  String get displayPaymentStatus {
    switch (this.status) {
      case 0:
        return 'order_payment_status_0'.tr;
      case 1:
        return 'order_payment_status_1'.tr;
      case 2:
        return 'order_payment_status_2'.tr;
      case 3:
        return 'order_payment_status_3'.tr;
      case 4:
        return 'order_payment_status_4'.tr;
      default:
        return 'status_unknown'.tr;
    }
  }
}

extension ExtensionOrderPayment on OrderPayment {
  num get paid {
    final json = jsonDecode(this.info ?? '{}');
    if (json.containsKey('paid')) {
      return json['paid'];
    }
    return 0;
  }

  num get change {
    final json = jsonDecode(this.info ?? '{}');
    if (json.containsKey('change')) {
      return json['change'];
    }
    return 0;
  }
}

extension ExtensionOrderDetail on OrderDetail {
  bool get containsInvoice {
    return invoice?.number?.isNotEmpty == true;
  }

  String get invoiceNumber => invoice?.number ?? '';

  Order toOrder() {
    return Order(
      createdAt: createdAt,
      id: id,
      memberName: memberName,
      orderNumber: orderNumber,
      paymentStatus: paymentStatus,
      status: status,
      storeAccountName: storeAccountName,
      total: total,
    );
  }

  Sunmi.Invoice toSunmiInvoice() {
    final invoiceModel = toInvoice();
    final prefProvider = Get.find<PrefProvider>();
    return Sunmi.Invoice(
      storeName: prefProvider.storeName,
      printMarkup: invoice?.invoicePaper ?? false,
      invoiceNumber: invoice?.number,
      invoiceTimestamp: invoice?.createdDateTime?.millisecondsSinceEpoch ??
          (createdDateTime?.microsecondsSinceEpoch ?? 0),
      randomNumber: invoice?.randomNumber,
      seller: prefProvider.taxId,
      buyer: invoice?.vatNumber,
      leftQr: invoiceModel.leftQrString,
      rightQr: invoiceModel.rightQrString,
      items: orderItems?.map((e) {
        return Sunmi.Item(
          name: e.productName,
          comment: '',
          count: 1, // 固定
          price: e.finalPrice,
          type: e.type,
          tax: prefProvider.taxType, // 1: 應稅(5%), 3: 免稅(農產品)
        );
      }).toList() ?? [],
    );
  }

  // TODO: try remove this.
  InvoiceModel toInvoice() {
    return InvoiceModel(
      invoiceNumber: invoice?.number,
      invoiceDate: invoice?.createdDateTime?.millisecondsSinceEpoch ?? 0,
      randomNumber: invoice?.randomNumber ?? '',
      totalAmount: total ?? 0,
      buyerIdentifier: invoice?.vatNumber ?? '',
      sellerIdentifier: Get.find<PrefProvider>()?.taxId ?? '',
      businessIdentifier: '83193989',
      productArray: jsonEncode(orderItems ?? []),
      printMark: (invoice?.invoicePaper ?? false) ? 1 : 0,
      productName: '',
      storeName: '',
    );
  }

  String get productName {
    final items = this.orderItems;
    if (items != null) {
      final index = items.indexWhere((e) => 0 == e.type);
      if (index >= 0) {
        final item = items.elementAt(index);
        return item.productName ?? '';
      }
    }
    return kDefaultItemName;
  }

  num get additionalCharges {
    final items = this.orderItems;
    if (items != null) {
      final index = items.indexWhere((e) => 4 == e.type);
      if (index >= 0) {
        final item = items.elementAt(index);
        return item.finalPrice ?? 0;
      }
    }
    return 0;
  }

  bool get isCompleted => 2 == this.status;

  DateTime? get createdDateTime {
    return utcToLocal(this.createdAt);
  }

  DateTime? get updatedDateTime => utcToLocal(updatedAt);

  DateTime? get dateTime {
    return utcToLocal(this.createdAt);
  }

  String get displayDateTimeMdHm {
    final time = this.dateTime;
    return time != null ? kDateTimeFormatMdHm.format(time) : '';
  }

  Color get displayStatusColor {
    switch (this.status) {
      case 0:
        return Colors.black;
      case 1:
        return Colors.black;
      case 2:
        return Colors.black;
      case 3:
        return Colors.red;
      case 4:
        return Colors.red;
      case 5:
        return Colors.red;
      default:
        return Colors.black;
    }
  }

  String get displayStatus {
    switch (this.status) {
      case 0:
        return 'order_status_0'.tr;
      case 1:
        return 'order_status_1'.tr;
      case 2:
        return 'order_status_2'.tr;
      case 3:
        return 'order_status_3'.tr;
      case 4:
        return 'order_status_4'.tr;
      case 5:
        // return 'order_status_5'.tr;
        return this.invoice != null ? '退款，作廢發票' : '退款';
      default:
        return 'status_unknown'.tr;
    }
  }

  String get displayPaymentStatus {
    switch (this.status) {
      case 0:
        return 'order_payment_status_0'.tr;
      case 1:
        return 'order_payment_status_1'.tr;
      case 2:
        return 'order_payment_status_2'.tr;
      case 3:
        return 'order_payment_status_3'.tr;
      case 4:
        return 'order_payment_status_4'.tr;
      default:
        return 'status_unknown'.tr;
    }
  }
}

extension ExtensionString on String {
  String takeLast(int n) => substring(length - n);
}

extension ExtensionInvoice on Invoice {
  DateTime? get createdDateTime => utcToLocal(createdAt);
}

extension ExtensionInvoiceModel on InvoiceModel {
  @visibleForTesting
  bool get testIsPrinted => _isPrinted;
  @visibleForTesting
  String testGetEncryptedString(String aesKey) => _getEncryptedString(aesKey);
  @visibleForTesting
  String get testCustomField => _customField;
  @visibleForTesting
  String get testItemCount => _itemCount;
  @visibleForTesting
  String get testStringEncode => _stringEncode;
  @visibleForTesting
  String get testBuyerIdentifier => _buyerIdentifier;
  // @visibleForTesting
  // String get testYear => _year;
  // @visibleForTesting
  // String get testTwYear => _twYear;
  // @visibleForTesting
  // String get testMonth => _month;
  // @visibleForTesting
  // String get testDay => _day;
  // @visibleForTesting
  // String get testTwYYYMMdd => _twYYYMMdd;
  @visibleForTesting
  String get testTotalHexWithoutTax => _totalHexWithoutTax;
  bool get _isPrinted => printMark is num && (printMark as num) > 0;

  String get displayPrintMark {
    final mark = _isPrinted ? '補印' : '';
    return '電子發票證明聯$mark';
  }

  double get printMarkFontSize => _isPrinted ? 30.0 : 36.0;

  // TODO: remove this
  DateTime get dateTime =>
      DateTime.fromMillisecondsSinceEpoch((invoiceDate ?? 0).toInt());

  // TODO: remove this
  String get displayDateTime => dateTime.yMdHms;

  String _getEncryptedString(String aesKey) {
    kLogger.d('key($aesKey)');
    final plainText = '$invoiceNumber$randomNumber';
    final key = Key.fromBase16(aesKey);
    final iv = IV.fromBase64(kIv);
    final encrypter = Encrypter(AES(key, mode: AESMode.cbc));
    final encrypted = encrypter.encrypt(plainText, iv: iv);
    // final result = encrypter.decrypt(encrypted, iv: iv);
    kLogger.d('plainText($plainText) encrypted(${encrypted.base64})');
    return encrypted.base64;
  }

  String get displayRandomNumber {
    return '隨機碼 $randomNumber';
  }

  String get fixedString {
    final value1 = invoiceNumber; // 發票字軌 ZJ71292204
    final value2 = dateTime.twYYYMMdd; // 發票開立日期 1090312
    final value3 = randomNumber; // 隨機碼 3612
    final value4 = _totalHexWithoutTax; // 銷售額 00000055 (85)
    final value5 = _totalHexWithTax; // 總計額 00000059 (89)
    final value6 = _buyerIdentifier; // 買方統一編號 00000000
    final value7 = sellerIdentifier; // 賣方統一編號 42308086
    final value8 = _getEncryptedString(kAESKey); // 加密驗證資訊
    return '$value1$value2$value3$value4$value5$value6$value7$value8';
  }

  String get leftString {
    // final value1 = invoiceNumber; // 發票字軌 ZJ71292204
    // final value2 = dateTime.twYYYMMdd; // 發票開立日期 1090312
    // final value3 = randomNumber; // 隨機碼 3612
    // final value4 = _totalHexWithoutTax; // 銷售額 00000055 (85)
    // final value5 = _totalHexWithTax; // 總計額 00000059 (89)
    // final value6 = _buyerIdentifier; // 買方統一編號 00000000
    // final value7 = sellerIdentifier; // 賣方統一編號 42308086
    // final value8 = _getEncryptedString(kAESKey); // 加密驗證資訊
    final value9 = _customField; // 營業人自行使用區 **********
    final value10 = _itemCount; // 二維條碼記載完整品 3
    final value11 = _itemCount; // 該張發票交易品目總筆數 3
    final value12 = _stringEncode; // 中文編碼參數 1
    final value13 = itemName;
    final value14 = itemCount;
    final value15 = itemPrice;
    final sector1 = fixedString;
    final sector2 =
        ':$value9:$value10:$value11:$value12:$value13:$value14:$value15:';
    kLogger.d('sector1($sector1)');
    return '$sector1$sector2';
  }

  String get leftQrString => leftString.padRight(128, ' ');

  String get displaySeller {
    sellerIdentifier ??= '';
    return '賣方$sellerIdentifier';
  }

  String get displayBuyer => hasBuyer ? '買方$buyerIdentifier' : '';

  bool get hasBuyer => buyerIdentifier?.isNotEmpty == true;

  String get displayInvoiceNumber {
    final value = invoiceNumber ?? '';
    // AB
    final regexpA = RegExp(r'^[A-Z]{2}');
    final matchA = regexpA.firstMatch(value);
    final newTextA = matchA?.group(0) ?? '';
    // 12345678
    final regexp0 = RegExp(r'[0-9]{1,8}');
    final match0 = regexp0.firstMatch(value);
    final newText0 = match0?.group(0) ?? '';
    final newText = '$newTextA-$newText0';
    //
    return newText;
  }

  String get barcode {
    final dt = dateTime;
    final value1 = dt.twYear;
    final mm = dt.month;
    final month = mm % 2 > 0 ? mm + 1 : mm;
    final value2 = '$month'.padLeft(2, '0');
    final value3 = invoiceNumber ?? '';
    final value4 = randomNumber;
    return '$value1$value2$value3$value4';
  }

  String get rightString {
    final value1 = '**';
    // final value2 = this.itemName;
    // final value3 = this.itemCount;
    // final value4 = this.itemPrice;
    // return '$value1$value2:$value3:$value4';
    return '$value1';
  }

  String get rightQrString => rightString.padRight(128, ' ');

  String get displayTwDateTime {
    final dt = dateTime;
    final twYear = dt.twYear;
    final month = dt.month;
    final currMonthString = dt.mm;
    if (dateTime.month % 2 > 0) {
      final nextMonth = month + 1;
      final nextMonthString = '$nextMonth'.padLeft(2, '0');
      return '$twYear年$currMonthString-$nextMonthString月';
    } else {
      final preMonth = month - 1;
      final preMonthString = '$preMonth'.padLeft(2, '0');
      return '$twYear年$preMonthString-$currMonthString月';
    }
  }

  String get itemName {
    productName ??= '';
    return productName!.isEmpty ? kDefaultItemName : productName!;
  }

  String get itemCount {
    // FIXME: user this.productArray
    return '1';
  }

  String get itemPrice {
    // final _ = this.totalAmount ?? 0;
    final prefProvider = Get.find<PrefProvider>();
    final taxType = prefProvider.taxType;
    final buyer = this.buyerIdentifier ?? '';
    final taxRateNormal = buyer.isNotEmpty ? kTaxRateNormal : 0.0;
    final taxRate = taxType == 1 ? taxRateNormal : kTaxRateFree;
    final price = this.totalAmount ?? 0.0;
    final untaxPrice = price / (1.0 + taxRate);
    final untaxPriceRound = untaxPrice.round();
    return untaxPriceRound.currency;
  }

  ///
  /// 該張發票交易品目總筆數：記錄該張發票記載消費品目總筆數，以十進位方式記載
  ///
  String get _itemCount {
    // FIXME: user this.productArray
    return '1';
  }

  ///
  /// 中文編碼參數 (1 碼)：定義後續資訊的編碼規格
  /// 0: big5
  /// 1: uft8
  /// 2: base64
  String get _stringEncode => '1';

  ///
  /// 營業人自行使用區 (10 碼)：提供營業人自行放置所需資訊，若不使用則以 10 個“*”符號呈現。
  ///
  String get _customField => ''.padLeft(10, '*');

  String get _buyerIdentifier =>
      hasBuyer ? (buyerIdentifier ?? '') : ''.padLeft(8, '0');

  set dateTime(DateTime value) => invoiceDate = value.millisecondsSinceEpoch.toInt();

  String get _totalHexWithTax {
    totalAmount ??= 0;
    final priceRound = totalAmount!.round();
    final t = priceRound.toRadixString(16);
    return '$t'.padLeft(8, '0');
  }

  String get _totalHexWithoutTax {
    final prefProvider = Get.find<PrefProvider>();
    final taxType = prefProvider.taxType;
    final buyer = buyerIdentifier ?? '';
    final taxRateNormal = buyer.isNotEmpty ? kTaxRateNormal : 0.0;
    final taxRate = taxType == 1 ? taxRateNormal : kTaxRateFree;
    final price = totalAmount ?? 0.0;
    final untaxPrice = price / (1.0 + taxRate);
    final untaxPriceRound = untaxPrice.round();
    final t = untaxPriceRound.toRadixString(16);
    return '$t'.padLeft(8, '0');
  }

  String get displayTotalAmount {
    totalAmount ??= 0;
    return '總計 ${totalAmount!.currency}';
  }
}

extension ExtensionPackageInfo on PackageInfo {
  String get displayVersion {
    final version = this.version;
    final build = this.buildNumber;
    return '$version ($build)';
  }
}

extension ExtensionBox<T> on Box<T> {
  Widget obx(
    NotifierBuilder<T> widget, {
    Widget Function(String error)? onError,
    Widget? onLoading,
    Widget? onEmpty,
    dynamic key,
  }) {
    return StreamBuilder<BoxEvent>(
      stream: this.watch(key: key),
      builder: (context, snapshot) {
        if (snapshot.hasError) {
          return onError != null
              ? onError(snapshot.error.toString())
              : Center(child: Text('A error occurred: ${snapshot.error}'));
        }
        if (snapshot.hasData) {
          return widget(snapshot.data!.value);
        }
        return onLoading ?? const Center(child: CircularProgressIndicator());
      },
    );
  }
}

extension ExtensionChannelsInfo on ChannelsInfo {
  ChannelsInfoOther get otherObject =>
      ChannelsInfoOther.fromRawJson(this.other ?? '');
}

extension ExtensionMap<K, V> on Map<K, V> {
  void removeNull() {
    removeWhere((key, value) => key == null || value == null);
  }

  Map<String, String?> toStringMap() {
    return map(
        (key, value) => MapEntry('$key', value == null ? null : '$value'));
  }
}

extension ExtensionDateTime on DateTime {
  // 09/13 12:00
  String get mmddHHmm => kDateTimeFormatMdHm.format(this);
  // 2021/09/14 16:00:15
  String get yMdHms => kDateTimeFormat.format(this);
  // 2021/09/03
  String get yMd => kDateFormat.format(this);
  String get dd => '$day'.padLeft(2, '0');
  String get yyyy => '$year'.padLeft(4, '0');
  String get twYear => '${year - 1911}';
  String get mm => '$month'.padLeft(2, '0');
  String get twYYYMMdd => '$twYear$mm$dd';
}

extension ExtensionGetStorage on GetStorage {
  Stream<String> watch([String? key]) async* {
    final streamController = StreamController<String>();
    Function? disposeListen;
    if (key != null && key.isNotEmpty) {
      disposeListen = listenKey(key, (e) {
        streamController.add(key);
      });
    } else {
      disposeListen = listen(() {
        streamController.add('');
      });
    }
    yield* streamController.stream;
    kLogger.d('[ExtensionGetStorage] watch finally');
    disposeListen?.call();
    streamController.close();
  }

  String get latestKey {
    final keys = List<String>.from(getKeys(), growable: false);
    if (keys.isEmpty) {
      return '';
    }
    // descending sort
    keys.sort((a, b) => b.compareTo(a));
    return keys.first;
  }

  bool isAfterLatestKey(String key) {
    final latest = num.tryParse(latestKey) ?? 0;
    final input = num.tryParse(key) ?? 0;
    return input > latest;
  }
}

extension GetInterfaceX on GetInterface {
  Future<T?> showLoading<T>() {
    return Progressing(
      message: '請稍候...',
    ).dialog<T>();
  }

  Future<T?> showAlert<T>(String message) {
    return DialogCustom.alert(
      contentText: message,
    ).dialog<T>();
  }
}

extension IterableX<T extends Widget> on Iterable<T> {
  Widget column({
    MainAxisAlignment mainAxisAlignment = MainAxisAlignment.start,
    CrossAxisAlignment crossAxisAlignment = CrossAxisAlignment.center,
    MainAxisSize mainAxisSize = MainAxisSize.min,
  }) {
    return Column(
      mainAxisAlignment: mainAxisAlignment,
      crossAxisAlignment: crossAxisAlignment,
      mainAxisSize: mainAxisSize,
      children: toList(growable: false),
    );
  }

  Widget row({
    MainAxisAlignment mainAxisAlignment = MainAxisAlignment.start,
    CrossAxisAlignment crossAxisAlignment = CrossAxisAlignment.center,
    MainAxisSize mainAxisSize = MainAxisSize.min,
  }) {
    return Row(
      mainAxisAlignment: mainAxisAlignment,
      crossAxisAlignment: crossAxisAlignment,
      mainAxisSize: mainAxisSize,
      children: toList(growable: false),
    );
  }
}
