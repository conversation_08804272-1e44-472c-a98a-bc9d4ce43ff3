{"info": {"_postman_id": "6747fa73-debd-4d0d-ac99-63bcb2db8b8b", "name": "OMOS POS APP API", "description": "版本格式：主版號.次版號.修訂號，版號遞增規則如下：<br>\n<br>\n主版號：當你做了不相容的 API 修改，<br>\n次版號：當你做了向下相容的功能性新增，<br>\n修訂號：當你做了向下相容的問題修正。<br>\n<br>\n先行版號及版本編譯資訊可以加到「主版號.次版號.修訂號」的後面，作為延伸。<br>\n<br>\nQR-Code 掃描後格式說明：\n  * `會員碼`：{ member_id: 1}\n  * `優惠碼`：{ member_id: 1, member_coupon_id: 1}\n  * `問卷核銷`：{ member_id: 1, member_questionnaire_id: 1}\n\n\nContact Support:\n Email: <EMAIL>", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "brands", "item": [{"name": "更新品牌資料", "request": {"method": "PUT", "header": [], "url": {"raw": "{{baseUrl}}/brands", "host": ["{{baseUrl}}"], "path": ["brands"]}}, "response": [{"name": "not found", "originalRequest": {"method": "PUT", "header": [{"description": "Added as a part of security scheme: bearer", "key": "Authorization", "value": "Bearer <token>"}], "url": {"raw": "{{baseUrl}}/brands", "host": ["{{baseUrl}}"], "path": ["brands"]}}, "status": "Not Found", "code": 404, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n \"error\": {\n  \"code\": 1104,\n  \"message\": \"找不到 *_id={id} 資料\"\n }\n}"}, {"name": "Bad Request", "originalRequest": {"method": "PUT", "header": [{"description": "Added as a part of security scheme: bearer", "key": "Authorization", "value": "Bearer <token>"}], "url": {"raw": "{{baseUrl}}/brands", "host": ["{{baseUrl}}"], "path": ["brands"]}}, "status": "Bad Request", "code": 400, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n \"error\": {\n  \"code\": 1101,\n  \"message\": \"required is empty\"\n }\n}"}, {"name": "Unexpected error", "originalRequest": {"method": "PUT", "header": [{"description": "Added as a part of security scheme: bearer", "key": "Authorization", "value": "Bearer <token>"}], "url": {"raw": "{{baseUrl}}/brands", "host": ["{{baseUrl}}"], "path": ["brands"]}}, "status": "Internal Server Error", "code": 500, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n \"code\": ********,\n \"message\": \"ipsum aliquip\"\n}"}, {"name": "OK", "originalRequest": {"method": "PUT", "header": [{"description": "Added as a part of security scheme: bearer", "key": "Authorization", "value": "Bearer <token>"}], "url": {"raw": "{{baseUrl}}/brands", "host": ["{{baseUrl}}"], "path": ["brands"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "null"}]}, {"name": "取得單一品牌資料", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/brands/info", "host": ["{{baseUrl}}"], "path": ["brands", "info"]}}, "response": [{"name": "Unexpected error", "originalRequest": {"method": "GET", "header": [{"description": "Added as a part of security scheme: bearer", "key": "Authorization", "value": "Bearer <token>"}], "url": {"raw": "{{baseUrl}}/brands/info", "host": ["{{baseUrl}}"], "path": ["brands", "info"]}}, "status": "Internal Server Error", "code": 500, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n \"code\": ********,\n \"message\": \"ipsum aliquip\"\n}"}, {"name": "OK", "originalRequest": {"method": "GET", "header": [{"description": "Added as a part of security scheme: bearer", "key": "Authorization", "value": "Bearer <token>"}], "url": {"raw": "{{baseUrl}}/brands/info", "host": ["{{baseUrl}}"], "path": ["brands", "info"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n \"data\": {\n  \"id\": 1,\n  \"name\": \"全家就是你家\",\n  \"code\": \"family_mart\",\n  \"phone\": \"(02)2222-5252\",\n  \"tax_id\": \"12345678\",\n  \"post_code\": \"12352\",\n  \"city\": {\n   \"id\": 1,\n   \"name\": \"台北市\"\n  },\n  \"cityare\": {\n   \"id\": 1,\n   \"name\": \"信義區\"\n  },\n  \"address\": \"民權東西1號\",\n  \"business_hours\": [\n   \"週一到週二 12:00 ~ 22:00, 週二公休\"\n  ],\n  \"point_ratio\": 1,\n  \"line_name\": \"全家@FamilyIsYourMart\",\n  \"line_channel_id\": \"1234567890\",\n  \"line_secret_code\": \"1wedga5di68jxskf9o2je84hfckls9\",\n  \"expiry_date\": \"2022-01-01\"\n }\n}"}, {"name": "not found", "originalRequest": {"method": "GET", "header": [{"description": "Added as a part of security scheme: bearer", "key": "Authorization", "value": "Bearer <token>"}], "url": {"raw": "{{baseUrl}}/brands/info", "host": ["{{baseUrl}}"], "path": ["brands", "info"]}}, "status": "Not Found", "code": 404, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n \"error\": {\n  \"code\": 1104,\n  \"message\": \"找不到 *_id={id} 資料\"\n }\n}"}]}]}, {"name": "channels", "item": [{"name": "更新通路資料", "request": {"method": "PUT", "header": [], "url": {"raw": "{{baseUrl}}/channels", "host": ["{{baseUrl}}"], "path": ["channels"]}}, "response": [{"name": "OK", "originalRequest": {"method": "PUT", "header": [{"description": "Added as a part of security scheme: bearer", "key": "Authorization", "value": "Bearer <token>"}], "url": {"raw": "{{baseUrl}}/channels", "host": ["{{baseUrl}}"], "path": ["channels"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "null"}, {"name": "not found", "originalRequest": {"method": "PUT", "header": [{"description": "Added as a part of security scheme: bearer", "key": "Authorization", "value": "Bearer <token>"}], "url": {"raw": "{{baseUrl}}/channels", "host": ["{{baseUrl}}"], "path": ["channels"]}}, "status": "Not Found", "code": 404, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n \"error\": {\n  \"code\": 1104,\n  \"message\": \"找不到 *_id={id} 資料\"\n }\n}"}, {"name": "Unexpected error", "originalRequest": {"method": "PUT", "header": [{"description": "Added as a part of security scheme: bearer", "key": "Authorization", "value": "Bearer <token>"}], "url": {"raw": "{{baseUrl}}/channels", "host": ["{{baseUrl}}"], "path": ["channels"]}}, "status": "Internal Server Error", "code": 500, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n \"code\": ********,\n \"message\": \"ipsum aliquip\"\n}"}, {"name": "Bad Request", "originalRequest": {"method": "PUT", "header": [{"description": "Added as a part of security scheme: bearer", "key": "Authorization", "value": "Bearer <token>"}], "url": {"raw": "{{baseUrl}}/channels", "host": ["{{baseUrl}}"], "path": ["channels"]}}, "status": "Bad Request", "code": 400, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n \"error\": {\n  \"code\": 1101,\n  \"message\": \"required is empty\"\n }\n}"}]}, {"name": "取得單一通路資料", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/channels/info", "host": ["{{baseUrl}}"], "path": ["channels", "info"]}}, "response": [{"name": "Unexpected error", "originalRequest": {"method": "GET", "header": [{"description": "Added as a part of security scheme: bearer", "key": "Authorization", "value": "Bearer <token>"}], "url": {"raw": "{{baseUrl}}/channels/info", "host": ["{{baseUrl}}"], "path": ["channels", "info"]}}, "status": "Internal Server Error", "code": 500, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n \"code\": ********,\n \"message\": \"ipsum aliquip\"\n}"}, {"name": "OK", "originalRequest": {"method": "GET", "header": [{"description": "Added as a part of security scheme: bearer", "key": "Authorization", "value": "Bearer <token>"}], "url": {"raw": "{{baseUrl}}/channels/info", "host": ["{{baseUrl}}"], "path": ["channels", "info"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n \"data\": {\n  \"id\": 1,\n  \"type\": null,\n  \"name\": \"忠孝店\",\n  \"code\": \"zhōngxiào\",\n  \"city\": {\n   \"id\": 1,\n   \"name\": \"台北市\"\n  },\n  \"cityarea\": {\n   \"id\": 1,\n   \"name\": \"信義區\"\n  },\n  \"address\": \"民權東西1-1號\",\n  \"business_hours\": [\n   \"週一到週二 12:00 ~ 22:00, 週二公休\"\n  ],\n  \"redeem_points\": true,\n  \"cash_ratio\": 1000,\n  \"usage_period\": 7,\n  \"usage_limit\": true,\n  \"min_prices\": 100,\n  \"restricted_types\": 1,\n  \"redeem_ratio\": 100,\n  \"max_redeem\": 100,\n  \"expiry_date\": \"2022-01-01\",\n  \"create_time\": \"2020-01-01 13:20:29\",\n  \"update_time\": \"2020-01-07 14:30:21\"\n }\n}"}, {"name": "not found", "originalRequest": {"method": "GET", "header": [{"description": "Added as a part of security scheme: bearer", "key": "Authorization", "value": "Bearer <token>"}], "url": {"raw": "{{baseUrl}}/channels/info", "host": ["{{baseUrl}}"], "path": ["channels", "info"]}}, "status": "Not Found", "code": 404, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n \"error\": {\n  \"code\": 1104,\n  \"message\": \"找不到 *_id={id} 資料\"\n }\n}"}]}]}, {"name": "members", "item": [{"name": "品牌會員資料", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/members?page=1&limit=50&keyword=<string>", "host": ["{{baseUrl}}"], "path": ["members"], "query": [{"key": "page", "value": "1", "description": "the current page"}, {"key": "limit", "value": "50", "description": "records per page, default is 50, maximum is 500"}, {"key": "keyword", "value": "<string>", "description": "操作員會員名稱或手機末3碼"}]}}, "response": [{"name": "Unexpected error", "originalRequest": {"method": "GET", "header": [{"description": "Added as a part of security scheme: bearer", "key": "Authorization", "value": "Bearer <token>"}], "url": {"raw": "{{baseUrl}}/members?page=1&limit=50&keyword=<string>", "host": ["{{baseUrl}}"], "path": ["members"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "50"}, {"key": "keyword", "value": "<string>"}]}}, "status": "Internal Server Error", "code": 500, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n \"code\": ********,\n \"message\": \"ipsum aliquip\"\n}"}, {"name": "OK", "originalRequest": {"method": "GET", "header": [{"description": "Added as a part of security scheme: bearer", "key": "Authorization", "value": "Bearer <token>"}], "url": {"raw": "{{baseUrl}}/members?page=1&limit=50&keyword=<string>", "host": ["{{baseUrl}}"], "path": ["members"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "50"}, {"key": "keyword", "value": "<string>"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n \"pagination\": {\n  \"total\": 111,\n  \"per_page\": 50,\n  \"current_page\": 1,\n  \"last_page\": 2,\n  \"from\": 1,\n  \"to\": 50\n },\n \"data\": [\n  {\n   \"id\": 1,\n   \"auth\": {\n    \"type\": 0,\n    \"identifier\": \"12jds98o9jdasaASD912kjsdal1290kdal\"\n   },\n   \"avatar\": \"https://xxx.omos.tw/profile/xxx/xxx.png\",\n   \"gender\": 2,\n   \"name\": \"流川楓\",\n   \"email\": \"<EMAIL>\",\n   \"mobile_phone\": \"0900000123\",\n   \"status\": 1,\n   \"create_time\": \"2020-01-01 13:20:29\",\n   \"update_time\": \"2020-01-07 14:30:21\"\n  },\n  {\n   \"id\": 2,\n   \"auth\": {\n    \"type\": 0,\n    \"identifier\": \"1239832p-4ksd;fl9023kdfls;ads;fe9wi90\"\n   },\n   \"avatar\": \"https://xxx.omos.tw/profile/xxx/000.png\",\n   \"gender\": 1,\n   \"name\": \"楊小白\",\n   \"email\": \"\",\n   \"mobile_phone\": \"\",\n   \"status\": 0,\n   \"create_time\": \"2020-01-01 13:20:29\",\n   \"update_time\": \"2020-01-07 14:30:21\"\n  }\n ]\n}"}]}, {"name": "取得單一會員資料", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/members/:member_id", "host": ["{{baseUrl}}"], "path": ["members", ":member_id"], "variable": [{"key": "member_id", "value": "<integer>", "description": "(Required) 會員編號"}]}}, "response": [{"name": "Unexpected error", "originalRequest": {"method": "GET", "header": [{"description": "Added as a part of security scheme: bearer", "key": "Authorization", "value": "Bearer <token>"}], "url": {"raw": "{{baseUrl}}/members/:member_id", "host": ["{{baseUrl}}"], "path": ["members", ":member_id"], "variable": [{"key": "member_id"}]}}, "status": "Internal Server Error", "code": 500, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n \"code\": ********,\n \"message\": \"ipsum aliquip\"\n}"}, {"name": "not found", "originalRequest": {"method": "GET", "header": [{"description": "Added as a part of security scheme: bearer", "key": "Authorization", "value": "Bearer <token>"}], "url": {"raw": "{{baseUrl}}/members/:member_id", "host": ["{{baseUrl}}"], "path": ["members", ":member_id"], "variable": [{"key": "member_id"}]}}, "status": "Not Found", "code": 404, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n \"error\": {\n  \"code\": 1104,\n  \"message\": \"找不到 *_id={id} 資料\"\n }\n}"}, {"name": "OK", "originalRequest": {"method": "GET", "header": [{"description": "Added as a part of security scheme: bearer", "key": "Authorization", "value": "Bearer <token>"}], "url": {"raw": "{{baseUrl}}/members/:member_id", "host": ["{{baseUrl}}"], "path": ["members", ":member_id"], "variable": [{"key": "member_id"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n \"data\": {\n  \"id\": 1,\n  \"auth\": {\n   \"type\": 0,\n   \"identifier\": \"12jds98o9jdasaASD912kjsdal1290kdal\"\n  },\n  \"avatar\": \"https://xxx.omos.tw/profile/xxx/xxx.png\",\n  \"gender\": 2,\n  \"name\": \"流川楓\",\n  \"email\": \"<PERSON><EMAIL>\",\n  \"mobile_phone\": \"0900000123\",\n  \"status\": 1,\n  \"create_time\": \"2020-01-01 13:20:29\",\n  \"update_time\": \"2020-01-07 14:30:21\"\n }\n}"}]}]}, {"name": "member-points", "item": [{"name": "{member id}", "item": [{"name": "會員積點列表", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/member-points/:member_id?page=1&limit=50", "host": ["{{baseUrl}}"], "path": ["member-points", ":member_id"], "query": [{"key": "page", "value": "1", "description": "the current page"}, {"key": "limit", "value": "50", "description": "records per page, default is 50, maximum is 500"}], "variable": [{"key": "member_id", "value": "<integer>", "description": "(Required) [object Object]"}]}}, "response": [{"name": "Unexpected error", "originalRequest": {"method": "GET", "header": [{"description": "Added as a part of security scheme: bearer", "key": "Authorization", "value": "Bearer <token>"}], "url": {"raw": "{{baseUrl}}/member-points/:member_id?page=1&limit=50", "host": ["{{baseUrl}}"], "path": ["member-points", ":member_id"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "50"}], "variable": [{"key": "member_id"}]}}, "status": "Internal Server Error", "code": 500, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n \"code\": ********,\n \"message\": \"ipsum aliquip\"\n}"}, {"name": "OK", "originalRequest": {"method": "GET", "header": [{"description": "Added as a part of security scheme: bearer", "key": "Authorization", "value": "Bearer <token>"}], "url": {"raw": "{{baseUrl}}/member-points/:member_id?page=1&limit=50", "host": ["{{baseUrl}}"], "path": ["member-points", ":member_id"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "50"}], "variable": [{"key": "member_id"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n \"pagination\": {\n  \"total\": 111,\n  \"per_page\": 50,\n  \"current_page\": 1,\n  \"last_page\": 2,\n  \"from\": 1,\n  \"to\": 50\n },\n \"data\": [\n  {\n   \"id\": 1,\n   \"order_id\": 1,\n   \"points\": 108,\n   \"expiry_date\": \"2020-10-30\",\n   \"status\": 1,\n   \"comment\": \"訂單產生\",\n   \"create_time\": \"2020-01-01 13:20:29\",\n   \"update_time\": \"2020-01-07 14:30:21\"\n  },\n  {\n   \"id\": 3,\n   \"order_id\": \"\",\n   \"points\": -10,\n   \"expiry_date\": \"2020-10-30\",\n   \"status\": 1,\n   \"comment\": \"多給點數，手動扣回\",\n   \"create_time\": \"2020-01-01 13:20:29\",\n   \"update_time\": \"2020-01-07 14:30:21\"\n  },\n  {\n   \"id\": 5,\n   \"order_id\": \"\",\n   \"points\": 10,\n   \"expiry_date\": \"2020-10-30\",\n   \"status\": 1,\n   \"comment\": \"pos 失效，多給予10點\",\n   \"create_time\": \"2020-01-01 13:20:29\",\n   \"update_time\": \"2020-01-07 14:30:21\"\n  }\n ]\n}"}]}, {"name": "會員總積點", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/member-points/:member_id/total", "host": ["{{baseUrl}}"], "path": ["member-points", ":member_id", "total"], "variable": [{"key": "member_id", "value": "<integer>", "description": "(Required) [object Object]"}]}}, "response": [{"name": "OK", "originalRequest": {"method": "GET", "header": [{"description": "Added as a part of security scheme: bearer", "key": "Authorization", "value": "Bearer <token>"}], "url": {"raw": "{{baseUrl}}/member-points/:member_id/total", "host": ["{{baseUrl}}"], "path": ["member-points", ":member_id", "total"], "variable": [{"key": "member_id"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n \"points\": 888\n}"}, {"name": "Unexpected error", "originalRequest": {"method": "GET", "header": [{"description": "Added as a part of security scheme: bearer", "key": "Authorization", "value": "Bearer <token>"}], "url": {"raw": "{{baseUrl}}/member-points/:member_id/total", "host": ["{{baseUrl}}"], "path": ["member-points", ":member_id", "total"], "variable": [{"key": "member_id"}]}}, "status": "Internal Server Error", "code": 500, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n \"code\": ********,\n \"message\": \"ipsum aliquip\"\n}"}, {"name": "not found", "originalRequest": {"method": "GET", "header": [{"description": "Added as a part of security scheme: bearer", "key": "Authorization", "value": "Bearer <token>"}], "url": {"raw": "{{baseUrl}}/member-points/:member_id/total", "host": ["{{baseUrl}}"], "path": ["member-points", ":member_id", "total"], "variable": [{"key": "member_id"}]}}, "status": "Not Found", "code": 404, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n \"error\": {\n  \"code\": 1104,\n  \"message\": \"找不到 *_id={id} 資料\"\n }\n}"}]}]}, {"name": "新增/減少會員積點", "request": {"method": "POST", "header": [], "url": {"raw": "{{baseUrl}}/member-points", "host": ["{{baseUrl}}"], "path": ["member-points"]}}, "response": [{"name": "Unexpected error", "originalRequest": {"method": "POST", "header": [{"description": "Added as a part of security scheme: bearer", "key": "Authorization", "value": "Bearer <token>"}], "url": {"raw": "{{baseUrl}}/member-points", "host": ["{{baseUrl}}"], "path": ["member-points"]}}, "status": "Internal Server Error", "code": 500, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n \"code\": ********,\n \"message\": \"ipsum aliquip\"\n}"}, {"name": "Bad Request", "originalRequest": {"method": "POST", "header": [{"description": "Added as a part of security scheme: bearer", "key": "Authorization", "value": "Bearer <token>"}], "url": {"raw": "{{baseUrl}}/member-points", "host": ["{{baseUrl}}"], "path": ["member-points"]}}, "status": "Bad Request", "code": 400, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n \"error\": {\n  \"code\": 1101,\n  \"message\": \"required is empty\"\n }\n}"}, {"name": "OK", "originalRequest": {"method": "POST", "header": [{"description": "Added as a part of security scheme: bearer", "key": "Authorization", "value": "Bearer <token>"}], "url": {"raw": "{{baseUrl}}/member-points", "host": ["{{baseUrl}}"], "path": ["member-points"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "null"}]}]}, {"name": "member-coupons", "item": [{"name": "發放會員優惠卷", "request": {"method": "POST", "header": [], "url": {"raw": "{{baseUrl}}/member-coupons/give", "host": ["{{baseUrl}}"], "path": ["member-coupons", "give"]}}, "response": [{"name": "Unexpected error", "originalRequest": {"method": "POST", "header": [{"description": "Added as a part of security scheme: bearer", "key": "Authorization", "value": "Bearer <token>"}], "url": {"raw": "{{baseUrl}}/member-coupons/give", "host": ["{{baseUrl}}"], "path": ["member-coupons", "give"]}}, "status": "Internal Server Error", "code": 500, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n \"code\": ********,\n \"message\": \"ipsum aliquip\"\n}"}, {"name": "OK", "originalRequest": {"method": "POST", "header": [{"description": "Added as a part of security scheme: bearer", "key": "Authorization", "value": "Bearer <token>"}], "url": {"raw": "{{baseUrl}}/member-coupons/give", "host": ["{{baseUrl}}"], "path": ["member-coupons", "give"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "null"}]}, {"name": "使用會員優惠卷", "request": {"method": "PUT", "header": [], "url": {"raw": "{{baseUrl}}/member-coupons/used", "host": ["{{baseUrl}}"], "path": ["member-coupons", "used"]}}, "response": [{"name": "Unexpected error", "originalRequest": {"method": "PUT", "header": [{"description": "Added as a part of security scheme: bearer", "key": "Authorization", "value": "Bearer <token>"}], "url": {"raw": "{{baseUrl}}/member-coupons/used", "host": ["{{baseUrl}}"], "path": ["member-coupons", "used"]}}, "status": "Internal Server Error", "code": 500, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n \"code\": ********,\n \"message\": \"ipsum aliquip\"\n}"}, {"name": "OK", "originalRequest": {"method": "PUT", "header": [{"description": "Added as a part of security scheme: bearer", "key": "Authorization", "value": "Bearer <token>"}], "url": {"raw": "{{baseUrl}}/member-coupons/used", "host": ["{{baseUrl}}"], "path": ["member-coupons", "used"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "null"}]}]}, {"name": "orders", "item": [{"name": "訂單列表", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/orders?page=1&limit=50&client_id=<integer>&brand_id=<integer>&channel_id=<integer>&status=<integer>&keyword=<string>", "host": ["{{baseUrl}}"], "path": ["orders"], "query": [{"key": "page", "value": "1", "description": "the current page"}, {"key": "limit", "value": "50", "description": "records per page, default is 50, maximum is 500"}, {"key": "client_id", "value": "<integer>", "description": "(Required) 集團"}, {"key": "brand_id", "value": "<integer>", "description": "(Required) 品牌"}, {"key": "channel_id", "value": "<integer>", "description": "(Required) 通路"}, {"key": "status", "value": "<integer>", "description": "狀態\n  * `0`：處理中\n  * `1`：已確認\n  * `2`：訂單完成\n  * `3`：訂單取消\n  * `4`：訂單異常\n  * `5`：訂單退貨\n"}, {"key": "keyword", "value": "<string>", "description": "關鍵字"}]}}, "response": [{"name": "Unexpected error", "originalRequest": {"method": "GET", "header": [{"description": "Added as a part of security scheme: bearer", "key": "Authorization", "value": "Bearer <token>"}], "url": {"raw": "{{baseUrl}}/orders?page=1&limit=50&client_id=1&brand_id=1&channel_id=1&status=1&keyword=<string>", "host": ["{{baseUrl}}"], "path": ["orders"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "50"}, {"key": "client_id", "value": "1"}, {"key": "brand_id", "value": "1"}, {"key": "channel_id", "value": "1"}, {"key": "status", "value": "1"}, {"key": "keyword", "value": "<string>"}]}}, "status": "Internal Server Error", "code": 500, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n \"code\": ********,\n \"message\": \"ipsum aliquip\"\n}"}, {"name": "OK", "originalRequest": {"method": "GET", "header": [{"description": "Added as a part of security scheme: bearer", "key": "Authorization", "value": "Bearer <token>"}], "url": {"raw": "{{baseUrl}}/orders?page=1&limit=50&client_id=1&brand_id=1&channel_id=1&status=1&keyword=<string>", "host": ["{{baseUrl}}"], "path": ["orders"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "50"}, {"key": "client_id", "value": "1"}, {"key": "brand_id", "value": "1"}, {"key": "channel_id", "value": "1"}, {"key": "status", "value": "1"}, {"key": "keyword", "value": "<string>"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n \"pagination\": {\n  \"total\": 111,\n  \"per_page\": 50,\n  \"current_page\": 1,\n  \"last_page\": 2,\n  \"from\": 1,\n  \"to\": 50\n },\n \"data\": [\n  {\n   \"id\": 1,\n   \"store_account_name\": \"林姓員工\",\n   \"member_name\": \"流川楓\",\n   \"order_number\": \"omos2020012223\",\n   \"total\": 12,\n   \"payment_status\": 3,\n   \"status\": 1,\n   \"create_time\": \"2020-01-01 13:20:29\"\n  },\n  {\n   \"id\": 2,\n   \"store_account_name\": \"林姓員工\",\n   \"member_name\": \"林頭角\",\n   \"order_number\": \"omos20200110291\",\n   \"total\": 12000,\n   \"payment_status\": 0,\n   \"status\": 1,\n   \"create_time\": \"2020-01-01 13:20:29\"\n  }\n ]\n}"}]}, {"name": "新增訂單", "request": {"method": "POST", "header": [], "url": {"raw": "{{baseUrl}}/orders", "host": ["{{baseUrl}}"], "path": ["orders"]}}, "response": [{"name": "Bad Request", "originalRequest": {"method": "POST", "header": [{"description": "Added as a part of security scheme: bearer", "key": "Authorization", "value": "Bearer <token>"}], "url": {"raw": "{{baseUrl}}/orders", "host": ["{{baseUrl}}"], "path": ["orders"]}}, "status": "Bad Request", "code": 400, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n \"error\": {\n  \"code\": 1101,\n  \"message\": \"required is empty\"\n }\n}"}, {"name": "Unexpected error", "originalRequest": {"method": "POST", "header": [{"description": "Added as a part of security scheme: bearer", "key": "Authorization", "value": "Bearer <token>"}], "url": {"raw": "{{baseUrl}}/orders", "host": ["{{baseUrl}}"], "path": ["orders"]}}, "status": "Internal Server Error", "code": 500, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n \"code\": ********,\n \"message\": \"ipsum aliquip\"\n}"}, {"name": "OK", "originalRequest": {"method": "POST", "header": [{"description": "Added as a part of security scheme: bearer", "key": "Authorization", "value": "Bearer <token>"}], "url": {"raw": "{{baseUrl}}/orders", "host": ["{{baseUrl}}"], "path": ["orders"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "null"}]}, {"name": "取得單一消費訂單資料", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/orders/:order_id", "host": ["{{baseUrl}}"], "path": ["orders", ":order_id"], "variable": [{"key": "order_id", "value": "<integer>", "description": "(Required) 訂單編號"}]}}, "response": [{"name": "Unexpected error", "originalRequest": {"method": "GET", "header": [{"description": "Added as a part of security scheme: bearer", "key": "Authorization", "value": "Bearer <token>"}], "url": {"raw": "{{baseUrl}}/orders/:order_id", "host": ["{{baseUrl}}"], "path": ["orders", ":order_id"], "variable": [{"key": "order_id"}]}}, "status": "Internal Server Error", "code": 500, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n \"code\": ********,\n \"message\": \"ipsum aliquip\"\n}"}, {"name": "not found", "originalRequest": {"method": "GET", "header": [{"description": "Added as a part of security scheme: bearer", "key": "Authorization", "value": "Bearer <token>"}], "url": {"raw": "{{baseUrl}}/orders/:order_id", "host": ["{{baseUrl}}"], "path": ["orders", ":order_id"], "variable": [{"key": "order_id"}]}}, "status": "Not Found", "code": 404, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n \"error\": {\n  \"code\": 1104,\n  \"message\": \"找不到 *_id={id} 資料\"\n }\n}"}, {"name": "OK", "originalRequest": {"method": "GET", "header": [{"description": "Added as a part of security scheme: bearer", "key": "Authorization", "value": "Bearer <token>"}], "url": {"raw": "{{baseUrl}}/orders/:order_id", "host": ["{{baseUrl}}"], "path": ["orders", ":order_id"], "variable": [{"key": "order_id"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n \"data\": {\n  \"id\": 1,\n  \"order_number\": \"omos2020012223\",\n  \"member_name\": \"流川楓\",\n  \"member_phone\": \"0900000123\",\n  \"total\": 12,\n  \"payment_status\": 0,\n  \"redeem_member_points\": 30,\n  \"status\": 1,\n  \"create_time\": \"2020-01-01 13:20:29\",\n  \"update_time\": \"2020-01-01 13:20:29\",\n  \"coupon\": {\n   \"title\": \"拿鐵大杯兌換卷\",\n   \"promotion_type\": 0,\n   \"discount\": 1\n  },\n  \"promotion_coupons\": [\n   {\n    \"coupon_image_url\": \"https://xxx.xxxx/abc.png\",\n    \"coupon_name\": \"拿鐵大杯兌換卷\",\n    \"expiry_date\": \"2020-12:21 10:00:00\"\n   }\n  ],\n  \"point_log\": {\n   \"id\": 1,\n   \"init_points\": 1010,\n   \"max_redeem_points\": 71,\n   \"redeem_points\": -60,\n   \"purchase_reward_points\": 17,\n   \"promotion_reward_points\": 0,\n   \"coupon_reward_points\": 0,\n   \"final_points\": 1013\n  }\n }\n}"}]}, {"name": "訂單退款", "request": {"method": "PUT", "header": [], "url": {"raw": "{{baseUrl}}/orders/refund", "host": ["{{baseUrl}}"], "path": ["orders", "refund"]}}, "response": [{"name": "Unexpected error", "originalRequest": {"method": "PUT", "header": [{"description": "Added as a part of security scheme: bearer", "key": "Authorization", "value": "Bearer <token>"}], "url": {"raw": "{{baseUrl}}/orders/refund", "host": ["{{baseUrl}}"], "path": ["orders", "refund"]}}, "status": "Internal Server Error", "code": 500, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n \"code\": ********,\n \"message\": \"ipsum aliquip\"\n}"}, {"name": "Bad Request", "originalRequest": {"method": "PUT", "header": [{"description": "Added as a part of security scheme: bearer", "key": "Authorization", "value": "Bearer <token>"}], "url": {"raw": "{{baseUrl}}/orders/refund", "host": ["{{baseUrl}}"], "path": ["orders", "refund"]}}, "status": "Bad Request", "code": 400, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n \"error\": {\n  \"code\": 1101,\n  \"message\": \"required is empty\"\n }\n}"}, {"name": "OK", "originalRequest": {"method": "PUT", "header": [{"description": "Added as a part of security scheme: bearer", "key": "Authorization", "value": "Bearer <token>"}], "url": {"raw": "{{baseUrl}}/orders/refund", "host": ["{{baseUrl}}"], "path": ["orders", "refund"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "null"}]}]}, {"name": "store-accounts", "item": [{"name": "店員、店長帳號列表", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/store-accounts", "host": ["{{baseUrl}}"], "path": ["store-accounts"]}}, "response": [{"name": "Unexpected error", "originalRequest": {"method": "GET", "header": [{"description": "Added as a part of security scheme: bearer", "key": "Authorization", "value": "Bearer <token>"}], "url": {"raw": "{{baseUrl}}/store-accounts", "host": ["{{baseUrl}}"], "path": ["store-accounts"]}}, "status": "Internal Server Error", "code": 500, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n \"code\": ********,\n \"message\": \"ipsum aliquip\"\n}"}, {"name": "OK", "originalRequest": {"method": "GET", "header": [{"description": "Added as a part of security scheme: bearer", "key": "Authorization", "value": "Bearer <token>"}], "url": {"raw": "{{baseUrl}}/store-accounts", "host": ["{{baseUrl}}"], "path": ["store-accounts"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n \"data\": [\n  {\n   \"account_id\": 1,\n   \"role\": {\n    \"id\": 1,\n    \"name\": \"店長\"\n   },\n   \"username\": \"rox\",\n   \"name\": \"林姓員工\",\n   \"status\": 1,\n   \"comment\": \"\",\n   \"last_login\": \"2020-01-01 13:20:29\",\n   \"create_time\": \"2020-01-01 13:20:29\",\n   \"update_time\": \"2020-01-07 14:30:21\"\n  },\n  {\n   \"account_id\": 2,\n   \"role\": {\n    \"id\": 2,\n    \"name\": \"店員\"\n   },\n   \"username\": \"ham\",\n   \"name\": \"林重魔\",\n   \"status\": 1,\n   \"comment\": \"\",\n   \"last_login\": \"2020-01-01 13:20:29\",\n   \"create_time\": \"2020-01-01 13:20:29\",\n   \"update_time\": \"2020-01-07 14:30:21\"\n  },\n  {\n   \"account_id\": 3,\n   \"role\": {\n    \"id\": 2,\n    \"name\": \"店員\"\n   },\n   \"username\": \"alex\",\n   \"name\": \"左弄弄\",\n   \"status\": 0,\n   \"comment\": \"請他走人了\",\n   \"last_login\": \"2020-01-01 13:20:29\",\n   \"create_time\": \"2020-01-01 13:20:29\",\n   \"update_time\": \"2020-01-07 14:30:21\"\n  }\n ]\n}"}]}, {"name": "新增帳號", "request": {"method": "POST", "header": [], "url": {"raw": "{{baseUrl}}/store-accounts", "host": ["{{baseUrl}}"], "path": ["store-accounts"]}}, "response": [{"name": "Bad Request", "originalRequest": {"method": "POST", "header": [{"description": "Added as a part of security scheme: bearer", "key": "Authorization", "value": "Bearer <token>"}], "url": {"raw": "{{baseUrl}}/store-accounts", "host": ["{{baseUrl}}"], "path": ["store-accounts"]}}, "status": "Bad Request", "code": 400, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n \"error\": {\n  \"code\": 1101,\n  \"message\": \"required is empty\"\n }\n}"}, {"name": "Unexpected error", "originalRequest": {"method": "POST", "header": [{"description": "Added as a part of security scheme: bearer", "key": "Authorization", "value": "Bearer <token>"}], "url": {"raw": "{{baseUrl}}/store-accounts", "host": ["{{baseUrl}}"], "path": ["store-accounts"]}}, "status": "Internal Server Error", "code": 500, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n \"code\": ********,\n \"message\": \"ipsum aliquip\"\n}"}, {"name": "OK", "originalRequest": {"method": "POST", "header": [{"description": "Added as a part of security scheme: bearer", "key": "Authorization", "value": "Bearer <token>"}], "url": {"raw": "{{baseUrl}}/store-accounts", "host": ["{{baseUrl}}"], "path": ["store-accounts"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "null"}]}, {"name": "更新帳號", "request": {"method": "PUT", "header": [], "url": {"raw": "{{baseUrl}}/store-accounts", "host": ["{{baseUrl}}"], "path": ["store-accounts"]}}, "response": [{"name": "Unexpected error", "originalRequest": {"method": "PUT", "header": [{"description": "Added as a part of security scheme: bearer", "key": "Authorization", "value": "Bearer <token>"}], "url": {"raw": "{{baseUrl}}/store-accounts", "host": ["{{baseUrl}}"], "path": ["store-accounts"]}}, "status": "Internal Server Error", "code": 500, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n \"code\": ********,\n \"message\": \"ipsum aliquip\"\n}"}, {"name": "not found", "originalRequest": {"method": "PUT", "header": [{"description": "Added as a part of security scheme: bearer", "key": "Authorization", "value": "Bearer <token>"}], "url": {"raw": "{{baseUrl}}/store-accounts", "host": ["{{baseUrl}}"], "path": ["store-accounts"]}}, "status": "Not Found", "code": 404, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n \"error\": {\n  \"code\": 1104,\n  \"message\": \"找不到 *_id={id} 資料\"\n }\n}"}, {"name": "OK", "originalRequest": {"method": "PUT", "header": [{"description": "Added as a part of security scheme: bearer", "key": "Authorization", "value": "Bearer <token>"}], "url": {"raw": "{{baseUrl}}/store-accounts", "host": ["{{baseUrl}}"], "path": ["store-accounts"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "null"}, {"name": "Bad Request", "originalRequest": {"method": "PUT", "header": [{"description": "Added as a part of security scheme: bearer", "key": "Authorization", "value": "Bearer <token>"}], "url": {"raw": "{{baseUrl}}/store-accounts", "host": ["{{baseUrl}}"], "path": ["store-accounts"]}}, "status": "Bad Request", "code": 400, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n \"error\": {\n  \"code\": 1101,\n  \"message\": \"required is empty\"\n }\n}"}]}, {"name": "取得單一帳號資料", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/store-accounts/:account_id", "host": ["{{baseUrl}}"], "path": ["store-accounts", ":account_id"], "variable": [{"key": "account_id", "value": "<integer>", "description": "(Required) [object Object]"}]}}, "response": [{"name": "OK", "originalRequest": {"method": "GET", "header": [{"description": "Added as a part of security scheme: bearer", "key": "Authorization", "value": "Bearer <token>"}], "url": {"raw": "{{baseUrl}}/store-accounts/:account_id", "host": ["{{baseUrl}}"], "path": ["store-accounts", ":account_id"], "variable": [{"key": "account_id"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n \"data\": {\n  \"account_id\": 1,\n  \"role\": {\n   \"id\": 1,\n   \"name\": \"店長\"\n  },\n  \"name\": \"林姓員工\",\n  \"username\": \"rox\",\n  \"status\": 1,\n  \"comment\": \"很常遲到\",\n  \"last_login\": \"2020-01-01 13:20:29\",\n  \"create_time\": \"2020-01-01 13:20:29\",\n  \"update_time\": \"2020-01-07 14:30:21\"\n }\n}"}, {"name": "not found", "originalRequest": {"method": "GET", "header": [{"description": "Added as a part of security scheme: bearer", "key": "Authorization", "value": "Bearer <token>"}], "url": {"raw": "{{baseUrl}}/store-accounts/:account_id", "host": ["{{baseUrl}}"], "path": ["store-accounts", ":account_id"], "variable": [{"key": "account_id"}]}}, "status": "Not Found", "code": 404, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n \"error\": {\n  \"code\": 1104,\n  \"message\": \"找不到 *_id={id} 資料\"\n }\n}"}, {"name": "Unexpected error", "originalRequest": {"method": "GET", "header": [{"description": "Added as a part of security scheme: bearer", "key": "Authorization", "value": "Bearer <token>"}], "url": {"raw": "{{baseUrl}}/store-accounts/:account_id", "host": ["{{baseUrl}}"], "path": ["store-accounts", ":account_id"], "variable": [{"key": "account_id"}]}}, "status": "Internal Server Error", "code": 500, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n \"code\": ********,\n \"message\": \"ipsum aliquip\"\n}"}]}]}, {"name": "member-promotions", "item": [{"name": "會員活動資料：入會禮＋生日禮＋會員推薦", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/member-promotions", "host": ["{{baseUrl}}"], "path": ["member-promotions"]}}, "response": [{"name": "Unexpected error", "originalRequest": {"method": "GET", "header": [{"description": "Added as a part of security scheme: bearer", "key": "Authorization", "value": "Bearer <token>"}], "url": {"raw": "{{baseUrl}}/member-promotions", "host": ["{{baseUrl}}"], "path": ["member-promotions"]}}, "status": "Internal Server Error", "code": 500, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n \"code\": ********,\n \"message\": \"ipsum aliquip\"\n}"}, {"name": "OK", "originalRequest": {"method": "GET", "header": [{"description": "Added as a part of security scheme: bearer", "key": "Authorization", "value": "Bearer <token>"}], "url": {"raw": "{{baseUrl}}/member-promotions", "host": ["{{baseUrl}}"], "path": ["member-promotions"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n \"data\": {\n  \"client_id\": 1,\n  \"brand_id\": 1,\n  \"channel_id\": 3,\n  \"member_coupon_id\": 1,\n  \"member_status\": 1,\n  \"birthday_coupon_id\": 1,\n  \"birthday_status\": 1,\n  \"introduction_time_limit\": 0,\n  \"introduction_coupon_id\": 1,\n  \"introduction_status\": 1,\n  \"create_time\": \"2020-01-01 13:20:29\",\n  \"update_time\": \"2020-01-07 14:30:21\"\n }\n}"}]}, {"name": "更新會員活動", "request": {"method": "PUT", "header": [], "url": {"raw": "{{baseUrl}}/member-promotions", "host": ["{{baseUrl}}"], "path": ["member-promotions"]}}, "response": [{"name": "Unexpected error", "originalRequest": {"method": "PUT", "header": [{"description": "Added as a part of security scheme: bearer", "key": "Authorization", "value": "Bearer <token>"}], "url": {"raw": "{{baseUrl}}/member-promotions", "host": ["{{baseUrl}}"], "path": ["member-promotions"]}}, "status": "Internal Server Error", "code": 500, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n \"code\": ********,\n \"message\": \"ipsum aliquip\"\n}"}, {"name": "OK", "originalRequest": {"method": "PUT", "header": [{"description": "Added as a part of security scheme: bearer", "key": "Authorization", "value": "Bearer <token>"}], "url": {"raw": "{{baseUrl}}/member-promotions", "host": ["{{baseUrl}}"], "path": ["member-promotions"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "null"}, {"name": "Bad Request", "originalRequest": {"method": "PUT", "header": [{"description": "Added as a part of security scheme: bearer", "key": "Authorization", "value": "Bearer <token>"}], "url": {"raw": "{{baseUrl}}/member-promotions", "host": ["{{baseUrl}}"], "path": ["member-promotions"]}}, "status": "Bad Request", "code": 400, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n \"error\": {\n  \"code\": 1101,\n  \"message\": \"required is empty\"\n }\n}"}]}]}, {"name": "questionnaires", "item": [{"name": "{questionnaire id}", "item": [{"name": "取得單一問卷資料", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/questionnaires/:questionnaire_id", "host": ["{{baseUrl}}"], "path": ["questionnaires", ":questionnaire_id"], "variable": [{"key": "questionnaire_id", "value": "<integer>", "description": "(Required) 問卷編號"}]}}, "response": [{"name": "Unexpected error", "originalRequest": {"method": "GET", "header": [{"description": "Added as a part of security scheme: bearer", "key": "Authorization", "value": "Bearer <token>"}], "url": {"raw": "{{baseUrl}}/questionnaires/:questionnaire_id", "host": ["{{baseUrl}}"], "path": ["questionnaires", ":questionnaire_id"], "variable": [{"key": "questionnaire_id"}]}}, "status": "Internal Server Error", "code": 500, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n \"code\": ********,\n \"message\": \"ipsum aliquip\"\n}"}, {"name": "not found", "originalRequest": {"method": "GET", "header": [{"description": "Added as a part of security scheme: bearer", "key": "Authorization", "value": "Bearer <token>"}], "url": {"raw": "{{baseUrl}}/questionnaires/:questionnaire_id", "host": ["{{baseUrl}}"], "path": ["questionnaires", ":questionnaire_id"], "variable": [{"key": "questionnaire_id"}]}}, "status": "Not Found", "code": 404, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n \"error\": {\n  \"code\": 1104,\n  \"message\": \"找不到 *_id={id} 資料\"\n }\n}"}, {"name": "OK", "originalRequest": {"method": "GET", "header": [{"description": "Added as a part of security scheme: bearer", "key": "Authorization", "value": "Bearer <token>"}], "url": {"raw": "{{baseUrl}}/questionnaires/:questionnaire_id", "host": ["{{baseUrl}}"], "path": ["questionnaires", ":questionnaire_id"], "variable": [{"key": "questionnaire_id"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n \"data\": {\n  \"id\": 1,\n  \"title\": \"快閃店顧客問卷\",\n  \"coupon\": {\n   \"id\": 1,\n   \"title\": \"拿鐵大杯兌換卷\"\n  },\n  \"usage_count\": 10,\n  \"status\": 1,\n  \"create_time\": \"2020-01-01 13:20:29\",\n  \"update_time\": \"2020-01-07 14:30:21\",\n  \"questions\": [\n   {\n    \"id\": 1,\n    \"type\": 1,\n    \"question\": \"請問您如何得知本店\",\n    \"create_time\": \"2020-01-01 13:20:29\",\n    \"update_time\": \"2020-01-07 14:30:21\",\n    \"items\": [\n     {\n      \"id\": 1,\n      \"option\": \"親友介紹\",\n      \"sort_order\": 1,\n      \"create_time\": \"2020-01-01 13:20:29\",\n      \"update_time\": \"2020-01-07 14:30:21\"\n     },\n     {\n      \"id\": 2,\n      \"option\": \"本店官網\",\n      \"sort_order\": 4,\n      \"create_time\": \"2020-01-01 13:20:29\",\n      \"update_time\": \"2020-01-07 14:30:21\"\n     },\n     {\n      \"id\": 3,\n      \"option\": \"平面媒體\",\n      \"sort_order\": \"\",\n      \"create_time\": \"2020-01-01 13:20:29\",\n      \"update_time\": \"2020-01-07 14:30:21\"\n     }\n    ]\n   },\n   {\n    \"id\": 2,\n    \"type\": 2,\n    \"question\": \"請問今日點餐為何\",\n    \"create_time\": \"2020-01-01 13:20:29\",\n    \"update_time\": \"2020-01-07 14:30:21\",\n    \"items\": [\n     {\n      \"id\": 4,\n      \"option\": \"牛排\",\n      \"sort_order\": 2,\n      \"create_time\": \"2020-01-01 13:20:29\",\n      \"update_time\": \"2020-01-07 14:30:21\"\n     },\n     {\n      \"id\": 5,\n      \"option\": \"啤酒\",\n      \"sort_order\": 2,\n      \"create_time\": \"2020-01-01 13:20:29\",\n      \"update_time\": \"2020-01-07 14:30:21\"\n     }\n    ]\n   }\n  ]\n }\n}"}]}, {"name": "刪除單一問卷資料", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/questionnaires/:questionnaire_id", "host": ["{{baseUrl}}"], "path": ["questionnaires", ":questionnaire_id"], "variable": [{"key": "questionnaire_id", "value": "<integer>", "description": "(Required) 問卷編號"}]}}, "response": [{"name": "OK", "originalRequest": {"method": "DELETE", "header": [{"description": "Added as a part of security scheme: bearer", "key": "Authorization", "value": "Bearer <token>"}], "url": {"raw": "{{baseUrl}}/questionnaires/:questionnaire_id", "host": ["{{baseUrl}}"], "path": ["questionnaires", ":questionnaire_id"], "variable": [{"key": "questionnaire_id"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "null"}, {"name": "Unexpected error", "originalRequest": {"method": "DELETE", "header": [{"description": "Added as a part of security scheme: bearer", "key": "Authorization", "value": "Bearer <token>"}], "url": {"raw": "{{baseUrl}}/questionnaires/:questionnaire_id", "host": ["{{baseUrl}}"], "path": ["questionnaires", ":questionnaire_id"], "variable": [{"key": "questionnaire_id"}]}}, "status": "Internal Server Error", "code": 500, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n \"code\": ********,\n \"message\": \"ipsum aliquip\"\n}"}, {"name": "not found", "originalRequest": {"method": "DELETE", "header": [{"description": "Added as a part of security scheme: bearer", "key": "Authorization", "value": "Bearer <token>"}], "url": {"raw": "{{baseUrl}}/questionnaires/:questionnaire_id", "host": ["{{baseUrl}}"], "path": ["questionnaires", ":questionnaire_id"], "variable": [{"key": "questionnaire_id"}]}}, "status": "Not Found", "code": 404, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n \"error\": {\n  \"code\": 1104,\n  \"message\": \"找不到 *_id={id} 資料\"\n }\n}"}]}]}, {"name": "問卷列表", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/questionnaires?page=1&limit=50&status=<integer>", "host": ["{{baseUrl}}"], "path": ["questionnaires"], "query": [{"key": "page", "value": "1", "description": "the current page"}, {"key": "limit", "value": "50", "description": "records per page, default is 50, maximum is 500"}, {"key": "status", "value": "<integer>", "description": "(Required) 狀態\n  * `0`：關閉\n  * `1`：啟用\n"}]}}, "response": [{"name": "OK", "originalRequest": {"method": "GET", "header": [{"description": "Added as a part of security scheme: bearer", "key": "Authorization", "value": "Bearer <token>"}], "url": {"raw": "{{baseUrl}}/questionnaires?page=1&limit=50&status=1", "host": ["{{baseUrl}}"], "path": ["questionnaires"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "50"}, {"key": "status", "value": "1"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n \"pagination\": {\n  \"total\": 111,\n  \"per_page\": 50,\n  \"current_page\": 1,\n  \"last_page\": 2,\n  \"from\": 1,\n  \"to\": 50\n },\n \"data\": [\n  {\n   \"id\": 1,\n   \"title\": \"快閃店顧客問卷\",\n   \"coupon\": {\n    \"id\": 1,\n    \"title\": \"拿鐵大杯兌換卷\"\n   },\n   \"usage_count\": 10,\n   \"status\": 1,\n   \"create_time\": \"2020-01-01 13:20:29\",\n   \"update_time\": \"2020-01-07 14:30:21\"\n  },\n  {\n   \"id\": 2,\n   \"title\": \"花博區顧客問卷\",\n   \"coupon\": {\n    \"id\": 1,\n    \"title\": \"折50元\"\n   },\n   \"usage_count\": 10,\n   \"status\": 1,\n   \"create_time\": \"2020-01-01 13:20:29\",\n   \"update_time\": \"2020-01-07 14:30:21\"\n  }\n ]\n}"}, {"name": "Unexpected error", "originalRequest": {"method": "GET", "header": [{"description": "Added as a part of security scheme: bearer", "key": "Authorization", "value": "Bearer <token>"}], "url": {"raw": "{{baseUrl}}/questionnaires?page=1&limit=50&status=1", "host": ["{{baseUrl}}"], "path": ["questionnaires"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "50"}, {"key": "status", "value": "1"}]}}, "status": "Internal Server Error", "code": 500, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n \"code\": ********,\n \"message\": \"ipsum aliquip\"\n}"}]}, {"name": "新增問卷", "request": {"method": "POST", "header": [], "url": {"raw": "{{baseUrl}}/questionnaires", "host": ["{{baseUrl}}"], "path": ["questionnaires"]}}, "response": [{"name": "Bad Request", "originalRequest": {"method": "POST", "header": [{"description": "Added as a part of security scheme: bearer", "key": "Authorization", "value": "Bearer <token>"}], "url": {"raw": "{{baseUrl}}/questionnaires", "host": ["{{baseUrl}}"], "path": ["questionnaires"]}}, "status": "Bad Request", "code": 400, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n \"error\": {\n  \"code\": 1101,\n  \"message\": \"required is empty\"\n }\n}"}, {"name": "OK", "originalRequest": {"method": "POST", "header": [{"description": "Added as a part of security scheme: bearer", "key": "Authorization", "value": "Bearer <token>"}], "url": {"raw": "{{baseUrl}}/questionnaires", "host": ["{{baseUrl}}"], "path": ["questionnaires"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "null"}, {"name": "Unexpected error", "originalRequest": {"method": "POST", "header": [{"description": "Added as a part of security scheme: bearer", "key": "Authorization", "value": "Bearer <token>"}], "url": {"raw": "{{baseUrl}}/questionnaires", "host": ["{{baseUrl}}"], "path": ["questionnaires"]}}, "status": "Internal Server Error", "code": 500, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n \"code\": ********,\n \"message\": \"ipsum aliquip\"\n}"}]}, {"name": "更新問卷", "request": {"method": "PUT", "header": [], "url": {"raw": "{{baseUrl}}/questionnaires", "host": ["{{baseUrl}}"], "path": ["questionnaires"], "variable": [{"key": "questionnaire_id", "value": "<integer>", "description": "(Required) 問卷編號"}]}}, "response": [{"name": "Bad Request", "originalRequest": {"method": "PUT", "header": [{"description": "Added as a part of security scheme: bearer", "key": "Authorization", "value": "Bearer <token>"}], "url": {"raw": "{{baseUrl}}/questionnaires", "host": ["{{baseUrl}}"], "path": ["questionnaires"]}}, "status": "Bad Request", "code": 400, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n \"error\": {\n  \"code\": 1101,\n  \"message\": \"required is empty\"\n }\n}"}, {"name": "OK", "originalRequest": {"method": "PUT", "header": [{"description": "Added as a part of security scheme: bearer", "key": "Authorization", "value": "Bearer <token>"}], "url": {"raw": "{{baseUrl}}/questionnaires", "host": ["{{baseUrl}}"], "path": ["questionnaires"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "null"}, {"name": "Unexpected error", "originalRequest": {"method": "PUT", "header": [{"description": "Added as a part of security scheme: bearer", "key": "Authorization", "value": "Bearer <token>"}], "url": {"raw": "{{baseUrl}}/questionnaires", "host": ["{{baseUrl}}"], "path": ["questionnaires"]}}, "status": "Internal Server Error", "code": 500, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n \"code\": ********,\n \"message\": \"ipsum aliquip\"\n}"}]}]}, {"name": "coupons", "item": [{"name": "{coupon id}", "item": [{"name": "取得單一優惠卷資料", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/coupons/:coupon_id", "host": ["{{baseUrl}}"], "path": ["coupons", ":coupon_id"], "variable": [{"key": "coupon_id", "value": "<integer>", "description": "(Required) [object Object]"}]}}, "response": [{"name": "not found", "originalRequest": {"method": "GET", "header": [{"description": "Added as a part of security scheme: bearer", "key": "Authorization", "value": "Bearer <token>"}], "url": {"raw": "{{baseUrl}}/coupons/:coupon_id", "host": ["{{baseUrl}}"], "path": ["coupons", ":coupon_id"], "variable": [{"key": "coupon_id"}]}}, "status": "Not Found", "code": 404, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n \"error\": {\n  \"code\": 1104,\n  \"message\": \"找不到 *_id={id} 資料\"\n }\n}"}, {"name": "Unexpected error", "originalRequest": {"method": "GET", "header": [{"description": "Added as a part of security scheme: bearer", "key": "Authorization", "value": "Bearer <token>"}], "url": {"raw": "{{baseUrl}}/coupons/:coupon_id", "host": ["{{baseUrl}}"], "path": ["coupons", ":coupon_id"], "variable": [{"key": "coupon_id"}]}}, "status": "Internal Server Error", "code": 500, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n \"code\": ********,\n \"message\": \"ipsum aliquip\"\n}"}, {"name": "OK", "originalRequest": {"method": "GET", "header": [{"description": "Added as a part of security scheme: bearer", "key": "Authorization", "value": "Bearer <token>"}], "url": {"raw": "{{baseUrl}}/coupons/:coupon_id", "host": ["{{baseUrl}}"], "path": ["coupons", ":coupon_id"], "variable": [{"key": "coupon_id"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n \"data\": {\n  \"id\": 1,\n  \"title\": \"拿鐵大杯兌換卷\",\n  \"description\": \"來店前50名，無條件贈\",\n  \"image_url\": \"https://xxx.xxxx/abc.png\",\n  \"promotion_type\": 0,\n  \"target\": 1,\n  \"min_prices\": 0,\n  \"quantity_issued\": 10,\n  \"period_limit\": 10,\n  \"can_use_points\": 1,\n  \"is_parallel_other\": 1,\n  \"can_exchange\": 1,\n  \"status\": 1,\n  \"create_time\": \"2020-01-01 13:20:29\",\n  \"update_time\": \"2020-01-07 14:30:21\",\n  \"exchange\": {\n   \"exchange_points\": 100,\n   \"start_time\": \"2020-10-30 12:10:10\",\n   \"end_time\": \"2020-10-30 23:10:10\",\n   \"exchange_count_limit\": 10\n  },\n  \"discount\": 100\n }\n}"}]}, {"name": "刪除單一優惠卷資料", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/coupons/:coupon_id", "host": ["{{baseUrl}}"], "path": ["coupons", ":coupon_id"], "variable": [{"key": "coupon_id", "value": "<integer>", "description": "(Required) [object Object]"}]}}, "response": [{"name": "Unexpected error", "originalRequest": {"method": "DELETE", "header": [{"description": "Added as a part of security scheme: bearer", "key": "Authorization", "value": "Bearer <token>"}], "url": {"raw": "{{baseUrl}}/coupons/:coupon_id", "host": ["{{baseUrl}}"], "path": ["coupons", ":coupon_id"], "variable": [{"key": "coupon_id"}]}}, "status": "Internal Server Error", "code": 500, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n \"code\": ********,\n \"message\": \"ipsum aliquip\"\n}"}, {"name": "OK", "originalRequest": {"method": "DELETE", "header": [{"description": "Added as a part of security scheme: bearer", "key": "Authorization", "value": "Bearer <token>"}], "url": {"raw": "{{baseUrl}}/coupons/:coupon_id", "host": ["{{baseUrl}}"], "path": ["coupons", ":coupon_id"], "variable": [{"key": "coupon_id"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "null"}, {"name": "not found", "originalRequest": {"method": "DELETE", "header": [{"description": "Added as a part of security scheme: bearer", "key": "Authorization", "value": "Bearer <token>"}], "url": {"raw": "{{baseUrl}}/coupons/:coupon_id", "host": ["{{baseUrl}}"], "path": ["coupons", ":coupon_id"], "variable": [{"key": "coupon_id"}]}}, "status": "Not Found", "code": 404, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n \"error\": {\n  \"code\": 1104,\n  \"message\": \"找不到 *_id={id} 資料\"\n }\n}"}]}]}, {"name": "優惠卷列表", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/coupons?page=1&limit=50&status=0&keyword=<string>", "host": ["{{baseUrl}}"], "path": ["coupons"], "query": [{"key": "page", "value": "1", "description": "the current page"}, {"key": "limit", "value": "50", "description": "records per page, default is 50, maximum is 500"}, {"key": "status", "value": "0", "description": "狀態\n  * `0`：關閉\n  * `1`：啟用\n"}, {"key": "keyword", "value": "<string>", "description": "[object Object]"}]}}, "response": [{"name": "OK", "originalRequest": {"method": "GET", "header": [{"description": "Added as a part of security scheme: bearer", "key": "Authorization", "value": "Bearer <token>"}], "url": {"raw": "{{baseUrl}}/coupons?page=1&limit=50&status=1&keyword=拿鐵大杯兌換卷", "host": ["{{baseUrl}}"], "path": ["coupons"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "50"}, {"key": "status", "value": "1"}, {"key": "keyword", "value": "拿鐵大杯兌換卷"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n \"pagination\": {\n  \"total\": 111,\n  \"per_page\": 50,\n  \"current_page\": 1,\n  \"last_page\": 2,\n  \"from\": 1,\n  \"to\": 50\n },\n \"data\": [\n  {\n   \"id\": 1,\n   \"title\": \"拿鐵大杯兌換卷\",\n   \"quantity_issued\": 10,\n   \"status\": 1,\n   \"can_exchange\": 1,\n   \"create_time\": \"2020-01-01 13:20:29\",\n   \"update_time\": \"2020-01-07 14:30:21\",\n   \"exchange\": {\n    \"id\": 1,\n    \"exchange_points\": 100,\n    \"start_time\": \"2020-10-30 12:10:10\",\n    \"end_time\": \"2020-10-30 23:10:10\",\n    \"exchange_count_limit\": 10,\n    \"create_time\": \"2020-01-01 13:20:29\",\n    \"update_time\": \"2020-01-07 14:30:21\"\n   }\n  },\n  {\n   \"id\": 2,\n   \"title\": \"任意品項折50元\",\n   \"quantity_issued\": 10,\n   \"status\": 1,\n   \"can_exchange\": 0,\n   \"create_time\": \"2020-01-01 13:20:29\",\n   \"update_time\": \"2020-01-07 14:30:21\",\n   \"exchange\": {}\n  }\n ]\n}"}, {"name": "Unexpected error", "originalRequest": {"method": "GET", "header": [{"description": "Added as a part of security scheme: bearer", "key": "Authorization", "value": "Bearer <token>"}], "url": {"raw": "{{baseUrl}}/coupons?page=1&limit=50&status=1&keyword=拿鐵大杯兌換卷", "host": ["{{baseUrl}}"], "path": ["coupons"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "50"}, {"key": "status", "value": "1"}, {"key": "keyword", "value": "拿鐵大杯兌換卷"}]}}, "status": "Internal Server Error", "code": 500, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n \"code\": ********,\n \"message\": \"ipsum aliquip\"\n}"}]}, {"name": "新增優惠卷", "request": {"method": "POST", "header": [], "url": {"raw": "{{baseUrl}}/coupons", "host": ["{{baseUrl}}"], "path": ["coupons"]}}, "response": [{"name": "Unexpected error", "originalRequest": {"method": "POST", "header": [{"description": "Added as a part of security scheme: bearer", "key": "Authorization", "value": "Bearer <token>"}], "url": {"raw": "{{baseUrl}}/coupons", "host": ["{{baseUrl}}"], "path": ["coupons"]}}, "status": "Internal Server Error", "code": 500, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n \"code\": ********,\n \"message\": \"ipsum aliquip\"\n}"}, {"name": "OK", "originalRequest": {"method": "POST", "header": [{"description": "Added as a part of security scheme: bearer", "key": "Authorization", "value": "Bearer <token>"}], "url": {"raw": "{{baseUrl}}/coupons", "host": ["{{baseUrl}}"], "path": ["coupons"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "null"}, {"name": "Bad Request", "originalRequest": {"method": "POST", "header": [{"description": "Added as a part of security scheme: bearer", "key": "Authorization", "value": "Bearer <token>"}], "url": {"raw": "{{baseUrl}}/coupons", "host": ["{{baseUrl}}"], "path": ["coupons"]}}, "status": "Bad Request", "code": 400, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n \"error\": {\n  \"code\": 1101,\n  \"message\": \"required is empty\"\n }\n}"}]}, {"name": "更新優惠卷", "request": {"method": "PUT", "header": [], "url": {"raw": "{{baseUrl}}/coupons", "host": ["{{baseUrl}}"], "path": ["coupons"]}}, "response": [{"name": "Unexpected error", "originalRequest": {"method": "PUT", "header": [{"description": "Added as a part of security scheme: bearer", "key": "Authorization", "value": "Bearer <token>"}], "url": {"raw": "{{baseUrl}}/coupons", "host": ["{{baseUrl}}"], "path": ["coupons"]}}, "status": "Internal Server Error", "code": 500, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n \"code\": ********,\n \"message\": \"ipsum aliquip\"\n}"}, {"name": "OK", "originalRequest": {"method": "PUT", "header": [{"description": "Added as a part of security scheme: bearer", "key": "Authorization", "value": "Bearer <token>"}], "url": {"raw": "{{baseUrl}}/coupons", "host": ["{{baseUrl}}"], "path": ["coupons"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "null"}, {"name": "Bad Request", "originalRequest": {"method": "PUT", "header": [{"description": "Added as a part of security scheme: bearer", "key": "Authorization", "value": "Bearer <token>"}], "url": {"raw": "{{baseUrl}}/coupons", "host": ["{{baseUrl}}"], "path": ["coupons"]}}, "status": "Bad Request", "code": 400, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n \"error\": {\n  \"code\": 1101,\n  \"message\": \"required is empty\"\n }\n}"}, {"name": "not found", "originalRequest": {"method": "PUT", "header": [{"description": "Added as a part of security scheme: bearer", "key": "Authorization", "value": "Bearer <token>"}], "url": {"raw": "{{baseUrl}}/coupons", "host": ["{{baseUrl}}"], "path": ["coupons"]}}, "status": "Not Found", "code": 404, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n \"error\": {\n  \"code\": 1104,\n  \"message\": \"找不到 *_id={id} 資料\"\n }\n}"}]}]}, {"name": "promotions/coupon", "item": [{"name": "{promotion id}", "item": [{"name": "取得單一優惠活動資料", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/promotions/coupon/:promotion_id", "host": ["{{baseUrl}}"], "path": ["promotions", "coupon", ":promotion_id"], "variable": [{"key": "promotion_id", "value": "<integer>", "description": "(Required) [object Object]"}]}}, "response": [{"name": "Unexpected error", "originalRequest": {"method": "GET", "header": [{"description": "Added as a part of security scheme: bearer", "key": "Authorization", "value": "Bearer <token>"}], "url": {"raw": "{{baseUrl}}/promotions/coupon/:promotion_id", "host": ["{{baseUrl}}"], "path": ["promotions", "coupon", ":promotion_id"], "variable": [{"key": "promotion_id"}]}}, "status": "Internal Server Error", "code": 500, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n \"code\": ********,\n \"message\": \"ipsum aliquip\"\n}"}, {"name": "OK", "originalRequest": {"method": "GET", "header": [{"description": "Added as a part of security scheme: bearer", "key": "Authorization", "value": "Bearer <token>"}], "url": {"raw": "{{baseUrl}}/promotions/coupon/:promotion_id", "host": ["{{baseUrl}}"], "path": ["promotions", "coupon", ":promotion_id"], "variable": [{"key": "promotion_id"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n \"data\": {\n  \"id\": 1,\n  \"title\": \"滿額即贈送優惠卷一張\",\n  \"type\": 1,\n  \"target\": 1,\n  \"start_time\": \"2020-10-30 12:10:10\",\n  \"end_time\": \"2020-10-30 23:10:10\",\n  \"usage_count_limit\": 1,\n  \"is_parallel_other\": 1,\n  \"can_use_points\": 1,\n  \"status\": 1,\n  \"create_time\": \"2020-01-01 13:20:29\",\n  \"update_time\": \"2020-01-07 14:30:21\",\n  \"point\": 130,\n  \"min_price\": 1,\n  \"coupon_id\": 1,\n  \"coupon_name\": \"拿鐵大杯兌換卷\"\n }\n}"}, {"name": "not found", "originalRequest": {"method": "GET", "header": [{"description": "Added as a part of security scheme: bearer", "key": "Authorization", "value": "Bearer <token>"}], "url": {"raw": "{{baseUrl}}/promotions/coupon/:promotion_id", "host": ["{{baseUrl}}"], "path": ["promotions", "coupon", ":promotion_id"], "variable": [{"key": "promotion_id"}]}}, "status": "Not Found", "code": 404, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n \"error\": {\n  \"code\": 1104,\n  \"message\": \"找不到 *_id={id} 資料\"\n }\n}"}]}, {"name": "刪除單一優惠活動資料", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/promotions/coupon/:promotion_id", "host": ["{{baseUrl}}"], "path": ["promotions", "coupon", ":promotion_id"], "variable": [{"key": "promotion_id", "value": "<integer>", "description": "(Required) [object Object]"}]}}, "response": [{"name": "not found", "originalRequest": {"method": "DELETE", "header": [{"description": "Added as a part of security scheme: bearer", "key": "Authorization", "value": "Bearer <token>"}], "url": {"raw": "{{baseUrl}}/promotions/coupon/:promotion_id", "host": ["{{baseUrl}}"], "path": ["promotions", "coupon", ":promotion_id"], "variable": [{"key": "promotion_id"}]}}, "status": "Not Found", "code": 404, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n \"error\": {\n  \"code\": 1104,\n  \"message\": \"找不到 *_id={id} 資料\"\n }\n}"}, {"name": "OK", "originalRequest": {"method": "DELETE", "header": [{"description": "Added as a part of security scheme: bearer", "key": "Authorization", "value": "Bearer <token>"}], "url": {"raw": "{{baseUrl}}/promotions/coupon/:promotion_id", "host": ["{{baseUrl}}"], "path": ["promotions", "coupon", ":promotion_id"], "variable": [{"key": "promotion_id"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "null"}, {"name": "Unexpected error", "originalRequest": {"method": "DELETE", "header": [{"description": "Added as a part of security scheme: bearer", "key": "Authorization", "value": "Bearer <token>"}], "url": {"raw": "{{baseUrl}}/promotions/coupon/:promotion_id", "host": ["{{baseUrl}}"], "path": ["promotions", "coupon", ":promotion_id"], "variable": [{"key": "promotion_id"}]}}, "status": "Internal Server Error", "code": 500, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n \"code\": ********,\n \"message\": \"ipsum aliquip\"\n}"}]}]}, {"name": "優惠活動列表", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/promotions/coupon?page=1&limit=50&status=0&keyword=<varchar>", "host": ["{{baseUrl}}"], "path": ["promotions", "coupon"], "query": [{"key": "page", "value": "1", "description": "the current page"}, {"key": "limit", "value": "50", "description": "records per page, default is 50, maximum is 500"}, {"key": "status", "value": "0", "description": "狀態\n  * `0`：關閉\n  * `1`：啟用\n"}, {"key": "keyword", "value": "<varchar>", "description": "活動標題"}]}}, "response": [{"name": "Unexpected error", "originalRequest": {"method": "GET", "header": [{"description": "Added as a part of security scheme: bearer", "key": "Authorization", "value": "Bearer <token>"}], "url": {"raw": "{{baseUrl}}/promotions/coupon?page=1&limit=50&status=1&keyword=滿額即贈送優惠卷一張", "host": ["{{baseUrl}}"], "path": ["promotions", "coupon"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "50"}, {"key": "status", "value": "1"}, {"key": "keyword", "value": "滿額即贈送優惠卷一張"}]}}, "status": "Internal Server Error", "code": 500, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n \"code\": ********,\n \"message\": \"ipsum aliquip\"\n}"}, {"name": "OK", "originalRequest": {"method": "GET", "header": [{"description": "Added as a part of security scheme: bearer", "key": "Authorization", "value": "Bearer <token>"}], "url": {"raw": "{{baseUrl}}/promotions/coupon?page=1&limit=50&status=1&keyword=滿額即贈送優惠卷一張", "host": ["{{baseUrl}}"], "path": ["promotions", "coupon"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "50"}, {"key": "status", "value": "1"}, {"key": "keyword", "value": "滿額即贈送優惠卷一張"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n \"pagination\": {\n  \"total\": 111,\n  \"per_page\": 50,\n  \"current_page\": 1,\n  \"last_page\": 2,\n  \"from\": 1,\n  \"to\": 50\n },\n \"data\": [\n  {\n   \"id\": 1,\n   \"title\": \"滿額即贈送優惠卷一張\",\n   \"start_time\": \"2020-10-30 12:10:10\",\n   \"end_time\": \"2020-10-30 23:10:10\",\n   \"status\": 1,\n   \"create_time\": \"2020-01-01 13:20:29\",\n   \"update_time\": \"2020-01-07 14:30:21\"\n  },\n  {\n   \"id\": 2,\n   \"title\": \"滿額即贈點數100點\",\n   \"start_time\": \"2020-10-30 12:10:10\",\n   \"end_time\": \"2020-10-30 23:10:10\",\n   \"status\": 0,\n   \"create_time\": \"2020-01-01 13:20:29\",\n   \"update_time\": \"2020-01-07 14:30:21\"\n  }\n ]\n}"}]}, {"name": "新增優惠活動", "request": {"method": "POST", "header": [], "url": {"raw": "{{baseUrl}}/promotions/coupon", "host": ["{{baseUrl}}"], "path": ["promotions", "coupon"]}}, "response": [{"name": "Bad Request", "originalRequest": {"method": "POST", "header": [{"description": "Added as a part of security scheme: bearer", "key": "Authorization", "value": "Bearer <token>"}], "url": {"raw": "{{baseUrl}}/promotions/coupon", "host": ["{{baseUrl}}"], "path": ["promotions", "coupon"]}}, "status": "Bad Request", "code": 400, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n \"error\": {\n  \"code\": 1101,\n  \"message\": \"required is empty\"\n }\n}"}, {"name": "OK", "originalRequest": {"method": "POST", "header": [{"description": "Added as a part of security scheme: bearer", "key": "Authorization", "value": "Bearer <token>"}], "url": {"raw": "{{baseUrl}}/promotions/coupon", "host": ["{{baseUrl}}"], "path": ["promotions", "coupon"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "null"}]}, {"name": "更新優惠活動", "request": {"method": "PUT", "header": [], "url": {"raw": "{{baseUrl}}/promotions/coupon", "host": ["{{baseUrl}}"], "path": ["promotions", "coupon"]}}, "response": [{"name": "not found", "originalRequest": {"method": "PUT", "header": [{"description": "Added as a part of security scheme: bearer", "key": "Authorization", "value": "Bearer <token>"}], "url": {"raw": "{{baseUrl}}/promotions/coupon", "host": ["{{baseUrl}}"], "path": ["promotions", "coupon"]}}, "status": "Not Found", "code": 404, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n \"error\": {\n  \"code\": 1104,\n  \"message\": \"找不到 *_id={id} 資料\"\n }\n}"}, {"name": "Bad Request", "originalRequest": {"method": "PUT", "header": [{"description": "Added as a part of security scheme: bearer", "key": "Authorization", "value": "Bearer <token>"}], "url": {"raw": "{{baseUrl}}/promotions/coupon", "host": ["{{baseUrl}}"], "path": ["promotions", "coupon"]}}, "status": "Bad Request", "code": 400, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n \"error\": {\n  \"code\": 1101,\n  \"message\": \"required is empty\"\n }\n}"}, {"name": "Unexpected error", "originalRequest": {"method": "PUT", "header": [{"description": "Added as a part of security scheme: bearer", "key": "Authorization", "value": "Bearer <token>"}], "url": {"raw": "{{baseUrl}}/promotions/coupon", "host": ["{{baseUrl}}"], "path": ["promotions", "coupon"]}}, "status": "Internal Server Error", "code": 500, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n \"code\": ********,\n \"message\": \"ipsum aliquip\"\n}"}, {"name": "OK", "originalRequest": {"method": "PUT", "header": [{"description": "Added as a part of security scheme: bearer", "key": "Authorization", "value": "Bearer <token>"}], "url": {"raw": "{{baseUrl}}/promotions/coupon", "host": ["{{baseUrl}}"], "path": ["promotions", "coupon"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "null"}]}]}, {"name": "cooperation-coupons", "item": [{"name": "{coupon id}", "item": [{"name": "取得單一跨界優惠卷資料", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/cooperation-coupons/:coupon_id", "host": ["{{baseUrl}}"], "path": ["cooperation-coupons", ":coupon_id"], "variable": [{"key": "coupon_id", "value": "<integer>", "description": "(Required) [object Object]"}]}}, "response": [{"name": "Unexpected error", "originalRequest": {"method": "GET", "header": [{"description": "Added as a part of security scheme: bearer", "key": "Authorization", "value": "Bearer <token>"}], "url": {"raw": "{{baseUrl}}/cooperation-coupons/:coupon_id", "host": ["{{baseUrl}}"], "path": ["cooperation-coupons", ":coupon_id"], "variable": [{"key": "coupon_id"}]}}, "status": "Internal Server Error", "code": 500, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n \"code\": ********,\n \"message\": \"ipsum aliquip\"\n}"}, {"name": "OK", "originalRequest": {"method": "GET", "header": [{"description": "Added as a part of security scheme: bearer", "key": "Authorization", "value": "Bearer <token>"}], "url": {"raw": "{{baseUrl}}/cooperation-coupons/:coupon_id", "host": ["{{baseUrl}}"], "path": ["cooperation-coupons", ":coupon_id"], "variable": [{"key": "coupon_id"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n \"data\": {\n  \"id\": 1,\n  \"title\": \"拿鐵大杯兌換卷\",\n  \"description\": \"來店前50名，無條件贈\",\n  \"image_url\": \"https://xxx.xxxx/abc.png\",\n  \"promotion_type\": 0,\n  \"target\": 1,\n  \"min_prices\": 0,\n  \"quantity_issued\": 10,\n  \"period_limit\": 10,\n  \"is_parallel_other\": 1,\n  \"status\": 1,\n  \"create_time\": \"2020-01-01 13:20:29\",\n  \"update_time\": \"2020-01-07 14:30:21\"\n }\n}"}, {"name": "not found", "originalRequest": {"method": "GET", "header": [{"description": "Added as a part of security scheme: bearer", "key": "Authorization", "value": "Bearer <token>"}], "url": {"raw": "{{baseUrl}}/cooperation-coupons/:coupon_id", "host": ["{{baseUrl}}"], "path": ["cooperation-coupons", ":coupon_id"], "variable": [{"key": "coupon_id"}]}}, "status": "Not Found", "code": 404, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n \"error\": {\n  \"code\": 1104,\n  \"message\": \"找不到 *_id={id} 資料\"\n }\n}"}]}, {"name": "刪除單一跨界優惠卷資料", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/cooperation-coupons/:coupon_id", "host": ["{{baseUrl}}"], "path": ["cooperation-coupons", ":coupon_id"], "variable": [{"key": "coupon_id", "value": "<integer>", "description": "(Required) [object Object]"}]}}, "response": [{"name": "Unexpected error", "originalRequest": {"method": "DELETE", "header": [{"description": "Added as a part of security scheme: bearer", "key": "Authorization", "value": "Bearer <token>"}], "url": {"raw": "{{baseUrl}}/cooperation-coupons/:coupon_id", "host": ["{{baseUrl}}"], "path": ["cooperation-coupons", ":coupon_id"], "variable": [{"key": "coupon_id"}]}}, "status": "Internal Server Error", "code": 500, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n \"code\": ********,\n \"message\": \"ipsum aliquip\"\n}"}, {"name": "OK", "originalRequest": {"method": "DELETE", "header": [{"description": "Added as a part of security scheme: bearer", "key": "Authorization", "value": "Bearer <token>"}], "url": {"raw": "{{baseUrl}}/cooperation-coupons/:coupon_id", "host": ["{{baseUrl}}"], "path": ["cooperation-coupons", ":coupon_id"], "variable": [{"key": "coupon_id"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "null"}, {"name": "not found", "originalRequest": {"method": "DELETE", "header": [{"description": "Added as a part of security scheme: bearer", "key": "Authorization", "value": "Bearer <token>"}], "url": {"raw": "{{baseUrl}}/cooperation-coupons/:coupon_id", "host": ["{{baseUrl}}"], "path": ["cooperation-coupons", ":coupon_id"], "variable": [{"key": "coupon_id"}]}}, "status": "Not Found", "code": 404, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n \"error\": {\n  \"code\": 1104,\n  \"message\": \"找不到 *_id={id} 資料\"\n }\n}"}]}]}, {"name": "跨界優惠卷列表", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/cooperation-coupons?page=1&limit=50&status=0&keyword=<string>", "host": ["{{baseUrl}}"], "path": ["cooperation-coupons"], "query": [{"key": "page", "value": "1", "description": "the current page"}, {"key": "limit", "value": "50", "description": "records per page, default is 50, maximum is 500"}, {"key": "status", "value": "0", "description": "狀態\n  * `0`：關閉\n  * `1`：啟用\n"}, {"key": "keyword", "value": "<string>", "description": "[object Object]"}]}}, "response": [{"name": "Unexpected error", "originalRequest": {"method": "GET", "header": [{"description": "Added as a part of security scheme: bearer", "key": "Authorization", "value": "Bearer <token>"}], "url": {"raw": "{{baseUrl}}/cooperation-coupons?page=1&limit=50&status=1&keyword=拿鐵大杯兌換卷", "host": ["{{baseUrl}}"], "path": ["cooperation-coupons"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "50"}, {"key": "status", "value": "1"}, {"key": "keyword", "value": "拿鐵大杯兌換卷"}]}}, "status": "Internal Server Error", "code": 500, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n \"code\": ********,\n \"message\": \"ipsum aliquip\"\n}"}, {"name": "OK", "originalRequest": {"method": "GET", "header": [{"description": "Added as a part of security scheme: bearer", "key": "Authorization", "value": "Bearer <token>"}], "url": {"raw": "{{baseUrl}}/cooperation-coupons?page=1&limit=50&status=1&keyword=拿鐵大杯兌換卷", "host": ["{{baseUrl}}"], "path": ["cooperation-coupons"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "50"}, {"key": "status", "value": "1"}, {"key": "keyword", "value": "拿鐵大杯兌換卷"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n \"pagination\": {\n  \"total\": 111,\n  \"per_page\": 50,\n  \"current_page\": 1,\n  \"last_page\": 2,\n  \"from\": 1,\n  \"to\": 50\n },\n \"data\": [\n  {\n   \"id\": 1,\n   \"title\": \"拿鐵大杯兌換卷\",\n   \"quantity_issued\": 10,\n   \"status\": 1,\n   \"can_exchange\": 1,\n   \"create_time\": \"2020-01-01 13:20:29\",\n   \"update_time\": \"2020-01-07 14:30:21\"\n  },\n  {\n   \"id\": 2,\n   \"title\": \"任意品項折50元\",\n   \"quantity_issued\": 10,\n   \"status\": 1,\n   \"can_exchange\": 0,\n   \"create_time\": \"2020-01-01 13:20:29\",\n   \"update_time\": \"2020-01-07 14:30:21\"\n  }\n ]\n}"}]}, {"name": "新增跨界優惠卷", "request": {"method": "POST", "header": [], "url": {"raw": "{{baseUrl}}/cooperation-coupons", "host": ["{{baseUrl}}"], "path": ["cooperation-coupons"]}}, "response": [{"name": "Unexpected error", "originalRequest": {"method": "POST", "header": [{"description": "Added as a part of security scheme: bearer", "key": "Authorization", "value": "Bearer <token>"}], "url": {"raw": "{{baseUrl}}/cooperation-coupons", "host": ["{{baseUrl}}"], "path": ["cooperation-coupons"]}}, "status": "Internal Server Error", "code": 500, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n \"code\": ********,\n \"message\": \"ipsum aliquip\"\n}"}, {"name": "OK", "originalRequest": {"method": "POST", "header": [{"description": "Added as a part of security scheme: bearer", "key": "Authorization", "value": "Bearer <token>"}], "url": {"raw": "{{baseUrl}}/cooperation-coupons", "host": ["{{baseUrl}}"], "path": ["cooperation-coupons"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "null"}, {"name": "Bad Request", "originalRequest": {"method": "POST", "header": [{"description": "Added as a part of security scheme: bearer", "key": "Authorization", "value": "Bearer <token>"}], "url": {"raw": "{{baseUrl}}/cooperation-coupons", "host": ["{{baseUrl}}"], "path": ["cooperation-coupons"]}}, "status": "Bad Request", "code": 400, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n \"error\": {\n  \"code\": 1101,\n  \"message\": \"required is empty\"\n }\n}"}]}, {"name": "更新跨界優惠卷", "request": {"method": "PUT", "header": [], "url": {"raw": "{{baseUrl}}/cooperation-coupons", "host": ["{{baseUrl}}"], "path": ["cooperation-coupons"]}}, "response": [{"name": "Unexpected error", "originalRequest": {"method": "PUT", "header": [{"description": "Added as a part of security scheme: bearer", "key": "Authorization", "value": "Bearer <token>"}], "url": {"raw": "{{baseUrl}}/cooperation-coupons", "host": ["{{baseUrl}}"], "path": ["cooperation-coupons"]}}, "status": "Internal Server Error", "code": 500, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n \"code\": ********,\n \"message\": \"ipsum aliquip\"\n}"}, {"name": "OK", "originalRequest": {"method": "PUT", "header": [{"description": "Added as a part of security scheme: bearer", "key": "Authorization", "value": "Bearer <token>"}], "url": {"raw": "{{baseUrl}}/cooperation-coupons", "host": ["{{baseUrl}}"], "path": ["cooperation-coupons"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "null"}, {"name": "not found", "originalRequest": {"method": "PUT", "header": [{"description": "Added as a part of security scheme: bearer", "key": "Authorization", "value": "Bearer <token>"}], "url": {"raw": "{{baseUrl}}/cooperation-coupons", "host": ["{{baseUrl}}"], "path": ["cooperation-coupons"]}}, "status": "Not Found", "code": 404, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n \"error\": {\n  \"code\": 1104,\n  \"message\": \"找不到 *_id={id} 資料\"\n }\n}"}, {"name": "Bad Request", "originalRequest": {"method": "PUT", "header": [{"description": "Added as a part of security scheme: bearer", "key": "Authorization", "value": "Bearer <token>"}], "url": {"raw": "{{baseUrl}}/cooperation-coupons", "host": ["{{baseUrl}}"], "path": ["cooperation-coupons"]}}, "status": "Bad Request", "code": 400, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n \"error\": {\n  \"code\": 1101,\n  \"message\": \"required is empty\"\n }\n}"}]}]}, {"name": "cooperation", "item": [{"name": "coupons", "item": [{"name": "{coupon id}", "item": [{"name": "取得單一跨界邀請資料", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/cooperation/coupons/:coupon_id", "host": ["{{baseUrl}}"], "path": ["cooperation", "coupons", ":coupon_id"], "variable": [{"key": "cooperation_id", "value": "<integer>", "description": "(Required) [object Object]"}]}}, "response": [{"name": "OK", "originalRequest": {"method": "GET", "header": [{"description": "Added as a part of security scheme: bearer", "key": "Authorization", "value": "Bearer <token>"}], "url": {"raw": "{{baseUrl}}/cooperation/coupons/:coupon_id", "host": ["{{baseUrl}}"], "path": ["cooperation", "coupons", ":coupon_id"], "variable": [{"key": "coupon_id"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n \"data\": {\n  \"id\": 1,\n  \"initiate_brand_name\": \"康是美\",\n  \"initiate_channel_id\": 1,\n  \"initiate_channel_name\": \"高雄店\",\n  \"initiate_brands_city\": \"高雄市\",\n  \"initiate_brands_cityare\": \"信義區\",\n  \"initiate_brands_address\": \"中正路346號\",\n  \"brand_name\": \"全家就是你家\",\n  \"channel_id\": 3,\n  \"channel_name\": \"忠孝店\",\n  \"brands_city\": \"台北市\",\n  \"brands_cityare\": \"信義區\",\n  \"brands_address\": \"忠孝東路4段3號\",\n  \"brands_phone\": \"(02)2222-5252\",\n  \"status\": 1,\n  \"create_time\": \"2020-01-01 13:20:29\",\n  \"update_time\": \"2020-01-07 14:30:21\",\n  \"initiate_channel_coupons\": [\n   {\n    \"coupon_id\": 1,\n    \"coupon_name\": \"拿鐵大杯兌換卷\",\n    \"create_time\": \"2020-01-01 13:20:29\",\n    \"update_time\": \"2020-01-07 14:30:21\"\n   }\n  ],\n  \"channel_coupons\": [\n   {\n    \"coupon_id\": 3,\n    \"coupon_name\": \"拿鐵小杯升級大杯\",\n    \"create_time\": \"2020-01-01 13:20:29\",\n    \"update_time\": \"2020-01-07 14:30:21\"\n   }\n  ]\n }\n}"}, {"name": "Unexpected error", "originalRequest": {"method": "GET", "header": [{"description": "Added as a part of security scheme: bearer", "key": "Authorization", "value": "Bearer <token>"}], "url": {"raw": "{{baseUrl}}/cooperation/coupons/:coupon_id", "host": ["{{baseUrl}}"], "path": ["cooperation", "coupons", ":coupon_id"], "variable": [{"key": "coupon_id"}]}}, "status": "Internal Server Error", "code": 500, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n \"code\": ********,\n \"message\": \"ipsum aliquip\"\n}"}, {"name": "not found", "originalRequest": {"method": "GET", "header": [{"description": "Added as a part of security scheme: bearer", "key": "Authorization", "value": "Bearer <token>"}], "url": {"raw": "{{baseUrl}}/cooperation/coupons/:coupon_id", "host": ["{{baseUrl}}"], "path": ["cooperation", "coupons", ":coupon_id"], "variable": [{"key": "coupon_id"}]}}, "status": "Not Found", "code": 404, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n \"error\": {\n  \"code\": 1104,\n  \"message\": \"找不到 *_id={id} 資料\"\n }\n}"}]}, {"name": "單一跨界優惠卷邀請狀態更新", "request": {"method": "PUT", "header": [], "url": {"raw": "{{baseUrl}}/cooperation/coupons/:coupon_id", "host": ["{{baseUrl}}"], "path": ["cooperation", "coupons", ":coupon_id"], "variable": [{"key": "coupon_id"}]}}, "response": [{"name": "not found", "originalRequest": {"method": "PUT", "header": [{"description": "Added as a part of security scheme: bearer", "key": "Authorization", "value": "Bearer <token>"}], "url": {"raw": "{{baseUrl}}/cooperation/coupons/:coupon_id", "host": ["{{baseUrl}}"], "path": ["cooperation", "coupons", ":coupon_id"], "variable": [{"key": "coupon_id"}]}}, "status": "Not Found", "code": 404, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n \"error\": {\n  \"code\": 1104,\n  \"message\": \"找不到 *_id={id} 資料\"\n }\n}"}, {"name": "OK", "originalRequest": {"method": "PUT", "header": [{"description": "Added as a part of security scheme: bearer", "key": "Authorization", "value": "Bearer <token>"}], "url": {"raw": "{{baseUrl}}/cooperation/coupons/:coupon_id", "host": ["{{baseUrl}}"], "path": ["cooperation", "coupons", ":coupon_id"], "variable": [{"key": "coupon_id"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "null"}, {"name": "Unexpected error", "originalRequest": {"method": "PUT", "header": [{"description": "Added as a part of security scheme: bearer", "key": "Authorization", "value": "Bearer <token>"}], "url": {"raw": "{{baseUrl}}/cooperation/coupons/:coupon_id", "host": ["{{baseUrl}}"], "path": ["cooperation", "coupons", ":coupon_id"], "variable": [{"key": "coupon_id"}]}}, "status": "Internal Server Error", "code": 500, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n \"code\": ********,\n \"message\": \"ipsum aliquip\"\n}"}]}]}, {"name": "搜尋跨界邀請", "request": {"method": "POST", "header": [], "url": {"raw": "{{baseUrl}}/cooperation/coupons/search?page=1&limit=50", "host": ["{{baseUrl}}"], "path": ["cooperation", "coupons", "search"], "query": [{"key": "page", "value": "1", "description": "the current page"}, {"key": "limit", "value": "50", "description": "records per page, default is 50, maximum is 500"}]}}, "response": [{"name": "Unexpected error", "originalRequest": {"method": "POST", "header": [{"description": "Added as a part of security scheme: bearer", "key": "Authorization", "value": "Bearer <token>"}], "url": {"raw": "{{baseUrl}}/cooperation/coupons/search?page=1&limit=50", "host": ["{{baseUrl}}"], "path": ["cooperation", "coupons", "search"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "50"}]}}, "status": "Internal Server Error", "code": 500, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n \"code\": ********,\n \"message\": \"ipsum aliquip\"\n}"}, {"name": "OK", "originalRequest": {"method": "POST", "header": [{"description": "Added as a part of security scheme: bearer", "key": "Authorization", "value": "Bearer <token>"}], "url": {"raw": "{{baseUrl}}/cooperation/coupons/search?page=1&limit=50", "host": ["{{baseUrl}}"], "path": ["cooperation", "coupons", "search"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "50"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n \"pagination\": {\n  \"total\": 111,\n  \"per_page\": 50,\n  \"current_page\": 1,\n  \"last_page\": 2,\n  \"from\": 1,\n  \"to\": 50\n },\n \"data\": [\n  {\n   \"id\": 1,\n   \"initiate_brand_name\": \"康是美\",\n   \"initiate_channel_id\": 1,\n   \"initiate_channel_name\": \"高雄店\",\n   \"brand_name\": \"全家就是你家\",\n   \"channel_id\": 3,\n   \"channel_name\": \"忠孝店\",\n   \"status\": 0,\n   \"create_time\": \"2020-01-01 13:20:29\",\n   \"update_time\": \"2020-01-07 14:30:21\",\n   \"coupons\": [\n    {\n     \"coupon_id\": 1,\n     \"coupon_name\": \"拿鐵大杯兌換卷\",\n     \"create_time\": \"2020-01-01 13:20:29\",\n     \"update_time\": \"2020-01-07 14:30:21\"\n    },\n    {\n     \"coupon_id\": 2,\n     \"coupon_name\": \"拿鐵小杯升級大杯\",\n     \"create_time\": \"2020-01-01 13:20:29\",\n     \"update_time\": \"2020-01-07 14:30:21\"\n    }\n   ]\n  },\n  {\n   \"id\": 3,\n   \"initiate_brand_name\": \"全家就是你家\",\n   \"initiate_channel_id\": 3,\n   \"initiate_channel_name\": \"忠孝店\",\n   \"brand_name\": \"康是美\",\n   \"channel_id\": 1,\n   \"channel_name\": \"高雄店\",\n   \"status\": 2,\n   \"create_time\": \"2020-01-01 13:20:29\",\n   \"update_time\": \"2020-01-07 14:30:21\",\n   \"coupons\": [\n    {\n     \"coupon_id\": 4,\n     \"coupon_name\": \"折抵 20 元\",\n     \"create_time\": \"2020-01-01 13:20:29\",\n     \"update_time\": \"2020-01-07 14:30:21\"\n    }\n   ]\n  }\n ]\n}"}]}]}, {"name": "跨界邀請列表", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/cooperation?page=1&limit=50&status=1&keyword=<string>", "host": ["{{baseUrl}}"], "path": ["cooperation"], "query": [{"key": "page", "value": "1", "description": "the current page"}, {"key": "limit", "value": "50", "description": "records per page, default is 50, maximum is 500"}, {"key": "status", "value": "1", "description": "[object Object]"}, {"key": "keyword", "value": "<string>", "description": "結盟品牌名稱"}]}}, "response": [{"name": "OK", "originalRequest": {"method": "GET", "header": [{"description": "Added as a part of security scheme: bearer", "key": "Authorization", "value": "Bearer <token>"}], "url": {"raw": "{{baseUrl}}/cooperation?page=1&limit=50&status=1&keyword=<string>", "host": ["{{baseUrl}}"], "path": ["cooperation"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "50"}, {"key": "status", "value": "1"}, {"key": "keyword", "value": "<string>"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n \"pagination\": {\n  \"total\": 111,\n  \"per_page\": 50,\n  \"current_page\": 1,\n  \"last_page\": 2,\n  \"from\": 1,\n  \"to\": 50\n },\n \"data\": [\n  {\n   \"id\": 1,\n   \"initiate_brand_name\": \"康是美\",\n   \"initiate_channel_id\": 1,\n   \"initiate_channel_name\": \"高雄店\",\n   \"brand_name\": \"全家就是你家\",\n   \"channel_id\": 3,\n   \"channel_name\": \"忠孝店\",\n   \"status\": 0,\n   \"create_time\": \"2020-01-01 13:20:29\",\n   \"update_time\": \"2020-01-07 14:30:21\",\n   \"coupons\": [\n    {\n     \"coupon_id\": 1,\n     \"coupon_name\": \"拿鐵大杯兌換卷\",\n     \"create_time\": \"2020-01-01 13:20:29\",\n     \"update_time\": \"2020-01-07 14:30:21\"\n    },\n    {\n     \"coupon_id\": 2,\n     \"coupon_name\": \"拿鐵小杯升級大杯\",\n     \"create_time\": \"2020-01-01 13:20:29\",\n     \"update_time\": \"2020-01-07 14:30:21\"\n    }\n   ]\n  },\n  {\n   \"id\": 3,\n   \"initiate_brand_name\": \"全家就是你家\",\n   \"initiate_channel_id\": 3,\n   \"initiate_channel_name\": \"忠孝店\",\n   \"brand_name\": \"康是美\",\n   \"channel_id\": 1,\n   \"channel_name\": \"高雄店\",\n   \"status\": 2,\n   \"create_time\": \"2020-01-01 13:20:29\",\n   \"update_time\": \"2020-01-07 14:30:21\",\n   \"coupons\": [\n    {\n     \"coupon_id\": 4,\n     \"coupon_name\": \"折抵 20 元\",\n     \"create_time\": \"2020-01-01 13:20:29\",\n     \"update_time\": \"2020-01-07 14:30:21\"\n    }\n   ]\n  }\n ]\n}"}, {"name": "Unexpected error", "originalRequest": {"method": "GET", "header": [{"description": "Added as a part of security scheme: bearer", "key": "Authorization", "value": "Bearer <token>"}], "url": {"raw": "{{baseUrl}}/cooperation?page=1&limit=50&status=1&keyword=<string>", "host": ["{{baseUrl}}"], "path": ["cooperation"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "50"}, {"key": "status", "value": "1"}, {"key": "keyword", "value": "<string>"}]}}, "status": "Internal Server Error", "code": 500, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n \"code\": ********,\n \"message\": \"ipsum aliquip\"\n}"}]}, {"name": "新增跨界邀請", "request": {"method": "POST", "header": [], "url": {"raw": "{{baseUrl}}/cooperation", "host": ["{{baseUrl}}"], "path": ["cooperation"]}}, "response": [{"name": "Bad Request", "originalRequest": {"method": "POST", "header": [{"description": "Added as a part of security scheme: bearer", "key": "Authorization", "value": "Bearer <token>"}], "url": {"raw": "{{baseUrl}}/cooperation", "host": ["{{baseUrl}}"], "path": ["cooperation"]}}, "status": "Bad Request", "code": 400, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n \"error\": {\n  \"code\": 1101,\n  \"message\": \"required is empty\"\n }\n}"}, {"name": "OK", "originalRequest": {"method": "POST", "header": [{"description": "Added as a part of security scheme: bearer", "key": "Authorization", "value": "Bearer <token>"}], "url": {"raw": "{{baseUrl}}/cooperation", "host": ["{{baseUrl}}"], "path": ["cooperation"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "null"}, {"name": "Unexpected error", "originalRequest": {"method": "POST", "header": [{"description": "Added as a part of security scheme: bearer", "key": "Authorization", "value": "Bearer <token>"}], "url": {"raw": "{{baseUrl}}/cooperation", "host": ["{{baseUrl}}"], "path": ["cooperation"]}}, "status": "Internal Server Error", "code": 500, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n \"code\": ********,\n \"message\": \"ipsum aliquip\"\n}"}]}, {"name": "更新跨界邀請", "request": {"method": "PUT", "header": [], "url": {"raw": "{{baseUrl}}/cooperation", "host": ["{{baseUrl}}"], "path": ["cooperation"]}}, "response": [{"name": "Bad Request", "originalRequest": {"method": "PUT", "header": [{"description": "Added as a part of security scheme: bearer", "key": "Authorization", "value": "Bearer <token>"}], "url": {"raw": "{{baseUrl}}/cooperation", "host": ["{{baseUrl}}"], "path": ["cooperation"]}}, "status": "Bad Request", "code": 400, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n \"error\": {\n  \"code\": 1101,\n  \"message\": \"required is empty\"\n }\n}"}, {"name": "OK", "originalRequest": {"method": "PUT", "header": [{"description": "Added as a part of security scheme: bearer", "key": "Authorization", "value": "Bearer <token>"}], "url": {"raw": "{{baseUrl}}/cooperation", "host": ["{{baseUrl}}"], "path": ["cooperation"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "null"}, {"name": "not found", "originalRequest": {"method": "PUT", "header": [{"description": "Added as a part of security scheme: bearer", "key": "Authorization", "value": "Bearer <token>"}], "url": {"raw": "{{baseUrl}}/cooperation", "host": ["{{baseUrl}}"], "path": ["cooperation"]}}, "status": "Not Found", "code": 404, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n \"error\": {\n  \"code\": 1104,\n  \"message\": \"找不到 *_id={id} 資料\"\n }\n}"}, {"name": "Unexpected error", "originalRequest": {"method": "PUT", "header": [{"description": "Added as a part of security scheme: bearer", "key": "Authorization", "value": "Bearer <token>"}], "url": {"raw": "{{baseUrl}}/cooperation", "host": ["{{baseUrl}}"], "path": ["cooperation"]}}, "status": "Internal Server Error", "code": 500, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n \"code\": ********,\n \"message\": \"ipsum aliquip\"\n}"}]}]}, {"name": "images", "item": [{"name": "{image id}", "item": [{"name": "取得單一圖片資料", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/images/:image_id?image_id=<integer>", "host": ["{{baseUrl}}"], "path": ["images", ":image_id"], "query": [{"key": "image_id", "value": "<integer>", "description": "(Required) [object Object]"}], "variable": [{"key": "image_id"}]}}, "response": [{"name": "not found", "originalRequest": {"method": "GET", "header": [{"description": "Added as a part of security scheme: bearer", "key": "Authorization", "value": "Bearer <token>"}], "url": {"raw": "{{baseUrl}}/images/:image_id?image_id=1", "host": ["{{baseUrl}}"], "path": ["images", ":image_id"], "query": [{"key": "image_id", "value": "1"}], "variable": [{"key": "image_id"}]}}, "status": "Not Found", "code": 404, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n \"error\": {\n  \"code\": 1104,\n  \"message\": \"找不到 *_id={id} 資料\"\n }\n}"}, {"name": "OK", "originalRequest": {"method": "GET", "header": [{"description": "Added as a part of security scheme: bearer", "key": "Authorization", "value": "Bearer <token>"}], "url": {"raw": "{{baseUrl}}/images/:image_id?image_id=1", "host": ["{{baseUrl}}"], "path": ["images", ":image_id"], "query": [{"key": "image_id", "value": "1"}], "variable": [{"key": "image_id"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n \"data\": {\n  \"id\": 1,\n  \"url\": \"https://xxx.xxxx/abc.png\",\n  \"size\": 333,\n  \"width\": 600,\n  \"height\": 600,\n  \"path\": \"/abc/aaa\",\n  \"create_time\": \"2020-01-01 13:20:29\",\n  \"update_time\": \"2020-01-07 14:30:21\"\n }\n}"}, {"name": "Unexpected error", "originalRequest": {"method": "GET", "header": [{"description": "Added as a part of security scheme: bearer", "key": "Authorization", "value": "Bearer <token>"}], "url": {"raw": "{{baseUrl}}/images/:image_id?image_id=1", "host": ["{{baseUrl}}"], "path": ["images", ":image_id"], "query": [{"key": "image_id", "value": "1"}], "variable": [{"key": "image_id"}]}}, "status": "Internal Server Error", "code": 500, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n \"code\": ********,\n \"message\": \"ipsum aliquip\"\n}"}]}, {"name": "刪除單一圖片資料", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/images/:image_id", "host": ["{{baseUrl}}"], "path": ["images", ":image_id"], "variable": [{"key": "image_id", "value": "<integer>", "description": "(Required) [object Object]"}]}}, "response": [{"name": "Unexpected error", "originalRequest": {"method": "DELETE", "header": [{"description": "Added as a part of security scheme: bearer", "key": "Authorization", "value": "Bearer <token>"}], "url": {"raw": "{{baseUrl}}/images/:image_id", "host": ["{{baseUrl}}"], "path": ["images", ":image_id"], "variable": [{"key": "image_id"}]}}, "status": "Internal Server Error", "code": 500, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n \"code\": ********,\n \"message\": \"ipsum aliquip\"\n}"}, {"name": "not found", "originalRequest": {"method": "DELETE", "header": [{"description": "Added as a part of security scheme: bearer", "key": "Authorization", "value": "Bearer <token>"}], "url": {"raw": "{{baseUrl}}/images/:image_id", "host": ["{{baseUrl}}"], "path": ["images", ":image_id"], "variable": [{"key": "image_id"}]}}, "status": "Not Found", "code": 404, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n \"error\": {\n  \"code\": 1104,\n  \"message\": \"找不到 *_id={id} 資料\"\n }\n}"}, {"name": "OK", "originalRequest": {"method": "DELETE", "header": [{"description": "Added as a part of security scheme: bearer", "key": "Authorization", "value": "Bearer <token>"}], "url": {"raw": "{{baseUrl}}/images/:image_id", "host": ["{{baseUrl}}"], "path": ["images", ":image_id"], "variable": [{"key": "image_id"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "null"}]}]}, {"name": "圖片庫列表", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/images?page=1&limit=50", "host": ["{{baseUrl}}"], "path": ["images"], "query": [{"key": "page", "value": "1", "description": "the current page"}, {"key": "limit", "value": "50", "description": "records per page, default is 50, maximum is 500"}]}}, "response": [{"name": "Unexpected error", "originalRequest": {"method": "GET", "header": [{"description": "Added as a part of security scheme: bearer", "key": "Authorization", "value": "Bearer <token>"}], "url": {"raw": "{{baseUrl}}/images?page=1&limit=50", "host": ["{{baseUrl}}"], "path": ["images"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "50"}]}}, "status": "Internal Server Error", "code": 500, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n \"code\": ********,\n \"message\": \"ipsum aliquip\"\n}"}, {"name": "OK", "originalRequest": {"method": "GET", "header": [{"description": "Added as a part of security scheme: bearer", "key": "Authorization", "value": "Bearer <token>"}], "url": {"raw": "{{baseUrl}}/images?page=1&limit=50", "host": ["{{baseUrl}}"], "path": ["images"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "50"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n \"pagination\": {\n  \"total\": 111,\n  \"per_page\": 50,\n  \"current_page\": 1,\n  \"last_page\": 2,\n  \"from\": 1,\n  \"to\": 50\n },\n \"data\": [\n  {\n   \"id\": 1,\n   \"url\": \"https://xxx.xxxx/abc.png\",\n   \"size\": 333,\n   \"width\": 600,\n   \"height\": 600,\n   \"path\": \"/abc/aaa\",\n   \"create_time\": \"2020-01-01 13:20:29\",\n   \"update_time\": \"2020-01-07 14:30:21\"\n  },\n  {\n   \"id\": 2,\n   \"url\": \"https://xxx.xxxx/bbb.png\",\n   \"size\": 33,\n   \"width\": 600,\n   \"height\": 600,\n   \"path\": \"/abc/bbb\",\n   \"create_time\": \"2020-01-01 13:20:29\",\n   \"update_time\": \"2020-01-07 14:30:21\"\n  }\n ]\n}"}]}, {"name": "圖片上傳", "request": {"method": "POST", "header": [], "url": {"raw": "{{baseUrl}}/images", "host": ["{{baseUrl}}"], "path": ["images"]}}, "response": [{"name": "Bad Request", "originalRequest": {"method": "POST", "header": [{"description": "Added as a part of security scheme: bearer", "key": "Authorization", "value": "Bearer <token>"}], "url": {"raw": "{{baseUrl}}/images", "host": ["{{baseUrl}}"], "path": ["images"]}}, "status": "Bad Request", "code": 400, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n \"error\": {\n  \"code\": 1101,\n  \"message\": \"required is empty\"\n }\n}"}, {"name": "Unexpected error", "originalRequest": {"method": "POST", "header": [{"description": "Added as a part of security scheme: bearer", "key": "Authorization", "value": "Bearer <token>"}], "url": {"raw": "{{baseUrl}}/images", "host": ["{{baseUrl}}"], "path": ["images"]}}, "status": "Internal Server Error", "code": 500, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n \"code\": ********,\n \"message\": \"ipsum aliquip\"\n}"}, {"name": "OK", "originalRequest": {"method": "POST", "header": [{"description": "Added as a part of security scheme: bearer", "key": "Authorization", "value": "Bearer <token>"}], "url": {"raw": "{{baseUrl}}/images", "host": ["{{baseUrl}}"], "path": ["images"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "null"}]}]}, {"name": "店長、店員登入", "request": {"method": "POST", "header": [], "url": {"raw": "{{baseUrl}}/login", "host": ["{{baseUrl}}"], "path": ["login"]}, "description": "使用帳號密碼登入（JWT 格式）"}, "response": [{"name": "錯誤的帳號密碼", "originalRequest": {"method": "POST", "header": [{"description": "Added as a part of security scheme: bearer", "key": "Authorization", "value": "Bearer <token>"}], "url": {"raw": "{{baseUrl}}/login", "host": ["{{baseUrl}}"], "path": ["login"]}}, "status": "Bad Request", "code": 400, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "text/plain"}], "cookie": [], "body": ""}, {"name": "OK：登入成功", "originalRequest": {"method": "POST", "header": [{"description": "Added as a part of security scheme: bearer", "key": "Authorization", "value": "Bearer <token>"}], "url": {"raw": "{{baseUrl}}/login", "host": ["{{baseUrl}}"], "path": ["login"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n \"old_token\": \"eyJhbGciOiJ11231IUzI1NiIsInR5cCI6IkpXVCJ9.eyJfaWQiOiI1ZDRlOGQ5YTA2MWMxYTJjMDIxY2JlMTgiLCJpYXQiOjE1NjU4NTczMjAsImV4cCI6MTU2NTk0MzcyMH0.GQVyQJLmwXd2jQZsjZ8n6cAWD0HQGjvlp2Mk8kAsGy8\",\n \"token\": \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJfaWQiOiI1ZDRlOGQ5YTA2MWMxYTJjMDIxY2JlMTgiLCJpYXQiOjE1NjU4NTczMjAsImV4cCI6MTU2NTk0MzcyMH0.GQVyQJLmwXd2jQZsjZ8n6cAWD0HQGjvlp2Mk8kAsGy8\"\n}"}]}, {"name": "店長、店員登出", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/logout", "host": ["{{baseUrl}}"], "path": ["logout"]}, "description": "登出系統"}, "response": [{"name": "OK：登入成功", "originalRequest": {"method": "GET", "header": [{"description": "Added as a part of security scheme: bearer", "key": "Authorization", "value": "Bearer <token>"}], "url": {"raw": "{{baseUrl}}/logout", "host": ["{{baseUrl}}"], "path": ["logout"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "null"}]}, {"name": "token 更新", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/renew", "host": ["{{baseUrl}}"], "path": ["renew"]}}, "response": [{"name": "錯誤的token資訊", "originalRequest": {"method": "GET", "header": [{"description": "Added as a part of security scheme: bearer", "key": "Authorization", "value": "Bearer <token>"}], "url": {"raw": "{{baseUrl}}/renew", "host": ["{{baseUrl}}"], "path": ["renew"]}}, "status": "Bad Request", "code": 400, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "text/plain"}], "cookie": [], "body": ""}, {"name": "OK：登入成功", "originalRequest": {"method": "GET", "header": [{"description": "Added as a part of security scheme: bearer", "key": "Authorization", "value": "Bearer <token>"}], "url": {"raw": "{{baseUrl}}/renew", "host": ["{{baseUrl}}"], "path": ["renew"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n \"token\": \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJfaWQiOiI1ZDRlOGQ5YTA2MWMxYTJjMDIxY2JlMTgiLCJpYXQiOjE1NjU4NTczMjAsImV4cCI6MTU2NTk0MzcyMH0.GQVyQJLmwXd2jQZsjZ8n6cAWD0HQGjvlp2Mk8kAsGy8\"\n}"}]}, {"name": "變更密碼", "request": {"method": "POST", "header": [], "url": {"raw": "{{baseUrl}}/profile/password-reset", "host": ["{{baseUrl}}"], "path": ["profile", "password-reset"]}, "description": "重新設定登入者密碼"}, "response": [{"name": "Unexpected error", "originalRequest": {"method": "POST", "header": [{"description": "Added as a part of security scheme: bearer", "key": "Authorization", "value": "Bearer <token>"}], "url": {"raw": "{{baseUrl}}/profile/password-reset", "host": ["{{baseUrl}}"], "path": ["profile", "password-reset"]}}, "status": "Internal Server Error", "code": 500, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n \"code\": ********,\n \"message\": \"ipsum aliquip\"\n}"}, {"name": "not found", "originalRequest": {"method": "POST", "header": [{"description": "Added as a part of security scheme: bearer", "key": "Authorization", "value": "Bearer <token>"}], "url": {"raw": "{{baseUrl}}/profile/password-reset", "host": ["{{baseUrl}}"], "path": ["profile", "password-reset"]}}, "status": "Not Found", "code": 404, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n \"error\": {\n  \"code\": 1104,\n  \"message\": \"找不到 *_id={id} 資料\"\n }\n}"}, {"name": "OK", "originalRequest": {"method": "POST", "header": [{"description": "Added as a part of security scheme: bearer", "key": "Authorization", "value": "Bearer <token>"}], "url": {"raw": "{{baseUrl}}/profile/password-reset", "host": ["{{baseUrl}}"], "path": ["profile", "password-reset"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "null"}]}, {"name": "取得單一集團資料", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/clients/info", "host": ["{{baseUrl}}"], "path": ["clients", "info"]}}, "response": [{"name": "OK", "originalRequest": {"method": "GET", "header": [{"description": "Added as a part of security scheme: bearer", "key": "Authorization", "value": "Bearer <token>"}], "url": {"raw": "{{baseUrl}}/clients/info", "host": ["{{baseUrl}}"], "path": ["clients", "info"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n \"data\": {\n  \"id\": 1,\n  \"group_name\": \"歐魔師集團\",\n  \"group_code\": \"omos\",\n  \"contact_name\": \"林魔師\",\n  \"contact_phone\": \"0900123456\",\n  \"contact_email\": \"<EMAIL>\",\n  \"create_time\": \"2020-01-01 13:20:29\",\n  \"update_time\": \"2020-01-07 14:30:21\"\n }\n}"}, {"name": "Unexpected error", "originalRequest": {"method": "GET", "header": [{"description": "Added as a part of security scheme: bearer", "key": "Authorization", "value": "Bearer <token>"}], "url": {"raw": "{{baseUrl}}/clients/info", "host": ["{{baseUrl}}"], "path": ["clients", "info"]}}, "status": "Internal Server Error", "code": 500, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n \"code\": ********,\n \"message\": \"ipsum aliquip\"\n}"}]}, {"name": "會員問卷核銷", "request": {"method": "PUT", "header": [], "url": {"raw": "{{baseUrl}}/member-questionnaires/used", "host": ["{{baseUrl}}"], "path": ["member-questionnaires", "used"]}}, "response": [{"name": "Unexpected error", "originalRequest": {"method": "PUT", "header": [{"description": "Added as a part of security scheme: bearer", "key": "Authorization", "value": "Bearer <token>"}], "url": {"raw": "{{baseUrl}}/member-questionnaires/used", "host": ["{{baseUrl}}"], "path": ["member-questionnaires", "used"]}}, "status": "Internal Server Error", "code": 500, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n \"code\": ********,\n \"message\": \"ipsum aliquip\"\n}"}, {"name": "OK", "originalRequest": {"method": "PUT", "header": [{"description": "Added as a part of security scheme: bearer", "key": "Authorization", "value": "Bearer <token>"}], "url": {"raw": "{{baseUrl}}/member-questionnaires/used", "host": ["{{baseUrl}}"], "path": ["member-questionnaires", "used"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "null"}]}, {"name": "購物車活動清單", "request": {"method": "POST", "header": [], "url": {"raw": "{{baseUrl}}/carts/promotion", "host": ["{{baseUrl}}"], "path": ["carts", "promotion"]}, "description": "購物車中符合活動的活動資訊"}, "response": [{"name": "OK", "originalRequest": {"method": "POST", "header": [{"description": "Added as a part of security scheme: bearer", "key": "Authorization", "value": "Bearer <token>"}], "url": {"raw": "{{baseUrl}}/carts/promotion", "host": ["{{baseUrl}}"], "path": ["carts", "promotion"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "[\n {\n  \"promotion_id\": 1,\n  \"title\": \"滿額即贈送優惠卷一張\",\n  \"type\": 1,\n  \"point\": 130,\n  \"coupon_id\": 1,\n  \"coupon_name\": \"拿鐵大杯兌換卷\"\n },\n {\n  \"promotion_id\": 1,\n  \"title\": \"滿額即贈送優惠卷一張\",\n  \"type\": 1,\n  \"point\": 130,\n  \"coupon_id\": 1,\n  \"coupon_name\": \"拿鐵大杯兌換卷\"\n }\n]"}, {"name": "Bad Request", "originalRequest": {"method": "POST", "header": [{"description": "Added as a part of security scheme: bearer", "key": "Authorization", "value": "Bearer <token>"}], "url": {"raw": "{{baseUrl}}/carts/promotion", "host": ["{{baseUrl}}"], "path": ["carts", "promotion"]}}, "status": "Bad Request", "code": 400, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n \"error\": {\n  \"code\": 1101,\n  \"message\": \"required is empty\"\n }\n}"}]}, {"name": "城市列表", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/cities", "host": ["{{baseUrl}}"], "path": ["cities"]}}, "response": [{"name": "OK", "originalRequest": {"method": "GET", "header": [{"description": "Added as a part of security scheme: bearer", "key": "Authorization", "value": "Bearer <token>"}], "url": {"raw": "{{baseUrl}}/cities", "host": ["{{baseUrl}}"], "path": ["cities"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n \"data\": [\n  {\n   \"id\": 1,\n   \"name\": \"台北市\"\n  },\n  {\n   \"id\": 2,\n   \"name\": \"新北市\"\n  }\n ]\n}"}, {"name": "Unexpected error", "originalRequest": {"method": "GET", "header": [{"description": "Added as a part of security scheme: bearer", "key": "Authorization", "value": "Bearer <token>"}], "url": {"raw": "{{baseUrl}}/cities", "host": ["{{baseUrl}}"], "path": ["cities"]}}, "status": "Internal Server Error", "code": 500, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n \"code\": ********,\n \"message\": \"ipsum aliquip\"\n}"}]}, {"name": "鄉鎮列表", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/cityares", "host": ["{{baseUrl}}"], "path": ["cityares"]}}, "response": [{"name": "Unexpected error", "originalRequest": {"method": "GET", "header": [{"description": "Added as a part of security scheme: bearer", "key": "Authorization", "value": "Bearer <token>"}], "url": {"raw": "{{baseUrl}}/cityares", "host": ["{{baseUrl}}"], "path": ["cityares"]}}, "status": "Internal Server Error", "code": 500, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n \"code\": ********,\n \"message\": \"ipsum aliquip\"\n}"}, {"name": "OK", "originalRequest": {"method": "GET", "header": [{"description": "Added as a part of security scheme: bearer", "key": "Authorization", "value": "Bearer <token>"}], "url": {"raw": "{{baseUrl}}/cityares", "host": ["{{baseUrl}}"], "path": ["cityares"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n \"data\": [\n  {\n   \"id\": 1,\n   \"city_id\": 1,\n   \"name\": \"信義區\"\n  },\n  {\n   \"id\": 2,\n   \"city_id\": 1,\n   \"name\": \"大安區\"\n  }\n ]\n}"}]}], "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "<Bearer <PERSON>>", "type": "string"}]}, "variable": [{"key": "baseUrl", "value": "http://api.omos.tw/pos", "type": "string"}]}